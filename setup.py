#!/usr/bin/env python3
"""
Setup script for Warehouse Management System
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="warehouse-management-system",
    version="1.0.0",
    author="Warehouse Management Team",
    author_email="<EMAIL>",
    description="نظام متكامل لإدارة فواتير المستودعات",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/warehouse-management/warehouse-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Database",
        "Natural Language :: Arabic",
        "Natural Language :: English",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "warehouse-management=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.sql"],
    },
    keywords="warehouse management inventory arabic english pyqt6 sqlite",
    project_urls={
        "Bug Reports": "https://github.com/warehouse-management/warehouse-system/issues",
        "Source": "https://github.com/warehouse-management/warehouse-system",
        "Documentation": "https://github.com/warehouse-management/warehouse-system/wiki",
    },
)
