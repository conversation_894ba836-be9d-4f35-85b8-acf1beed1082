[DATABASE]
# Database configuration
db_name = warehouse.db
backup_interval_days = 7
auto_backup = true
max_backups = 10

[APPLICATION]
# Application settings
app_name = نظام إدارة المستودعات
app_version = 1.0.0
default_language = ar
theme = dark
window_width = 1200
window_height = 800

[COMPANY]
# Company information
name = شركة إدارة المستودعات
name_en = Warehouse Management Company
address = 
phone = 
email = 
website = 
logo_path = 

[CURRENCY]
# Currency settings
symbol = ريال
symbol_en = SAR
decimal_places = 2
thousands_separator = ,
decimal_separator = .

[INVOICE]
# Invoice settings
purchase_prefix = PUR
sales_prefix = SAL
auto_generate_numbers = true
number_length = 10

[SECURITY]
# Security settings
session_timeout_minutes = 60
password_min_length = 6
require_password_change = false
max_login_attempts = 5
lockout_duration_minutes = 15

[BACKUP]
# Backup settings
backup_folder = backups
auto_backup_time = 02:00
compress_backups = true
backup_retention_days = 30

[EXPORT]
# Export settings
export_folder = exports
default_format = pdf
include_logo = true
page_size = A4
font_size = 10

[LOGGING]
# Logging settings
log_level = INFO
log_file = logs/warehouse.log
max_log_size_mb = 10
backup_count = 5

[NETWORK]
# Network settings (for future multi-user support)
enable_network = false
server_port = 8080
max_connections = 10

[UI]
# User interface settings
show_splash_screen = true
remember_window_position = true
show_tooltips = true
animation_enabled = true
confirm_delete = true

[REPORTS]
# Report settings
default_date_range = 30
show_charts = true
auto_refresh_interval = 300
cache_reports = true

[PRODUCTS]
# Product settings
auto_generate_codes = true
code_prefix = PRD
code_length = 10
require_images = false
image_max_size_mb = 5
allowed_image_formats = png,jpg,jpeg,bmp,gif

[INVENTORY]
# Inventory settings
track_serial_numbers = false
enable_low_stock_alerts = true
low_stock_threshold = 10
enable_expiry_tracking = false
default_unit = قطعة
