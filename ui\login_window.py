import sys
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QApplication, QWidget, QGridLayout, QSpacerItem,
                            QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QIcon
from database import DatabaseManager
from utils.translations import tr, translator
from ui.styles import get_login_style, COLORS

class LoginWindow(QDialog):
    login_successful = pyqtSignal(dict)  # Signal to emit user data on successful login
    
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.init_ui()
        self.apply_styles()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(tr('login'))
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.MSWindowsFixedSizeDialogHint)
        
        # Center the window
        self.center_window()
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create main container
        container = QFrame()
        container.setObjectName("loginContainer")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(40, 40, 40, 40)
        container_layout.setSpacing(20)
        
        # Logo/Title section
        title_layout = QVBoxLayout()
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # App title
        title_label = QLabel(tr('app_title'))
        title_label.setProperty("class", "title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel(tr('login'))
        subtitle_label.setProperty("class", "subtitle")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(subtitle_label)
        
        container_layout.addLayout(title_layout)
        
        # Add spacer
        container_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        
        # Login form
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # Username field
        username_layout = QVBoxLayout()
        username_layout.setSpacing(5)
        username_label = QLabel(tr('username'))
        username_label.setStyleSheet(f"color: {COLORS['text_secondary']}; font-weight: bold;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText(tr('username'))
        self.username_input.returnPressed.connect(self.login)
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        form_layout.addLayout(username_layout)
        
        # Password field
        password_layout = QVBoxLayout()
        password_layout.setSpacing(5)
        password_label = QLabel(tr('password'))
        password_label.setStyleSheet(f"color: {COLORS['text_secondary']}; font-weight: bold;")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText(tr('password'))
        self.password_input.returnPressed.connect(self.login)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        form_layout.addLayout(password_layout)
        
        container_layout.addLayout(form_layout)
        
        # Add spacer
        container_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        
        # Login button
        self.login_button = QPushButton(tr('login_button'))
        self.login_button.setProperty("class", "primary")
        self.login_button.clicked.connect(self.login)
        self.login_button.setMinimumHeight(45)
        container_layout.addWidget(self.login_button)
        
        # Add some spacing at bottom
        container_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        
        # Add container to main layout
        main_layout.addWidget(container)
        self.setLayout(main_layout)
        
        # Set focus to username input
        self.username_input.setFocus()
        
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def apply_styles(self):
        """Apply the login window styles"""
        self.setStyleSheet(get_login_style() + f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {COLORS['background']}, 
                    stop:1 {COLORS['primary']});
            }}
            
            #loginContainer {{
                background-color: {COLORS['surface']};
                border-radius: 12px;
                border: 1px solid {COLORS['border']};
            }}
            
            QLineEdit {{
                min-height: 40px;
                font-size: 11pt;
            }}
            
            QPushButton {{
                font-size: 12pt;
                font-weight: bold;
            }}
        """)
        
    def login(self):
        """Handle login attempt"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_error(tr('fill_required_fields'))
            return
            
        # Authenticate user
        user = self.db.authenticate_user(username, password)
        
        if user:
            # Successful login
            self.login_successful.emit(user)
            self.accept()
        else:
            # Failed login
            self.show_error(tr('invalid_credentials'))
            self.password_input.clear()
            self.password_input.setFocus()
            
    def show_error(self, message):
        """Show error message"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle(tr('login_failed'))
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        
        # Apply dark theme to message box
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {COLORS['surface']};
                color: {COLORS['text_primary']};
            }}
            QMessageBox QPushButton {{
                background-color: {COLORS['accent']};
                color: {COLORS['text_primary']};
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 60px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: #2980B9;
            }}
        """)
        
        msg_box.exec()
        
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName(tr('app_title'))
    app.setApplicationVersion("1.0")
    
    # Create and show login window
    login_window = LoginWindow()
    
    def on_login_success(user_data):
        print(f"Login successful: {user_data}")
        app.quit()
    
    login_window.login_successful.connect(on_login_success)
    
    if login_window.exec() == QDialog.DialogCode.Accepted:
        print("Login accepted")
    else:
        print("Login cancelled")
        
    sys.exit()
