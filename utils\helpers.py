"""
Helper functions for the warehouse management application
"""

import os
import shutil
import random
import string
from datetime import datetime
from PyQt6.QtWidgets import QMessageBox
from PyQt6.QtCore import QDate
from utils.translations import tr

def generate_invoice_number(prefix="INV"):
    """Generate a unique invoice number"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = ''.join(random.choices(string.digits, k=3))
    return f"{prefix}-{timestamp}-{random_suffix}"

def generate_barcode(length=13):
    """Generate a random barcode"""
    return ''.join(random.choices(string.digits, k=length))

def format_currency(amount, currency_symbol="ريال"):
    """Format currency amount"""
    return f"{amount:,.2f} {currency_symbol}"

def format_date(date_obj):
    """Format date for display"""
    if isinstance(date_obj, QDate):
        return date_obj.toString("yyyy-MM-dd")
    elif isinstance(date_obj, datetime):
        return date_obj.strftime("%Y-%m-%d")
    elif isinstance(date_obj, str):
        return date_obj
    else:
        return str(date_obj)

def validate_required_fields(fields_dict):
    """
    Validate required fields
    
    Args:
        fields_dict: Dictionary with field_name: (value, display_name) pairs
    
    Returns:
        tuple: (is_valid, error_message)
    """
    for field_name, (value, display_name) in fields_dict.items():
        if not value or (isinstance(value, str) and not value.strip()):
            return False, f"{display_name} {tr('is_required', 'is required')}"
    
    return True, ""

def show_error_message(parent, title, message):
    """Show error message dialog"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Icon.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg_box.exec()

def show_success_message(parent, title, message):
    """Show success message dialog"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Icon.Information)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg_box.exec()

def show_warning_message(parent, title, message):
    """Show warning message dialog"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Icon.Warning)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg_box.exec()

def confirm_action(parent, title, message):
    """Show confirmation dialog"""
    reply = QMessageBox.question(
        parent,
        title,
        message,
        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        QMessageBox.StandardButton.No
    )
    return reply == QMessageBox.StandardButton.Yes

def create_directory(path):
    """Create directory if it doesn't exist"""
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error creating directory {path}: {e}")
        return False

def copy_file(source, destination):
    """Copy file from source to destination"""
    try:
        # Create destination directory if it doesn't exist
        dest_dir = os.path.dirname(destination)
        create_directory(dest_dir)
        
        shutil.copy2(source, destination)
        return True
    except Exception as e:
        print(f"Error copying file from {source} to {destination}: {e}")
        return False

def delete_file(file_path):
    """Delete file if it exists"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
        return True
    except Exception as e:
        print(f"Error deleting file {file_path}: {e}")
        return False

def get_file_extension(file_path):
    """Get file extension"""
    return os.path.splitext(file_path)[1].lower()

def is_image_file(file_path):
    """Check if file is an image"""
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff']
    return get_file_extension(file_path) in image_extensions

def sanitize_filename(filename):
    """Sanitize filename by removing invalid characters"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def truncate_text(text, max_length=50):
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def get_current_timestamp():
    """Get current timestamp as string"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def parse_date_string(date_string, format_string="%Y-%m-%d"):
    """Parse date string to datetime object"""
    try:
        return datetime.strptime(date_string, format_string)
    except ValueError:
        return None

def calculate_percentage(part, total):
    """Calculate percentage"""
    if total == 0:
        return 0
    return (part / total) * 100

def round_currency(amount, decimals=2):
    """Round currency amount to specified decimal places"""
    return round(float(amount), decimals)

def is_valid_email(email):
    """Basic email validation"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_valid_phone(phone):
    """Basic phone number validation"""
    import re
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    # Check if it has reasonable length (7-15 digits)
    return 7 <= len(digits_only) <= 15

def escape_sql_like(text):
    """Escape special characters for SQL LIKE queries"""
    return text.replace('%', '\\%').replace('_', '\\_')

def get_app_data_dir():
    """Get application data directory"""
    app_name = "WarehouseManagement"
    
    if os.name == 'nt':  # Windows
        app_data = os.environ.get('APPDATA', '')
        return os.path.join(app_data, app_name)
    else:  # Linux/Mac
        home = os.path.expanduser('~')
        return os.path.join(home, f'.{app_name.lower()}')

def ensure_app_directories():
    """Ensure all required application directories exist"""
    base_dir = get_app_data_dir()
    directories = [
        base_dir,
        os.path.join(base_dir, 'images'),
        os.path.join(base_dir, 'images', 'products'),
        os.path.join(base_dir, 'backups'),
        os.path.join(base_dir, 'exports'),
        os.path.join(base_dir, 'logs')
    ]
    
    for directory in directories:
        create_directory(directory)
    
    return base_dir

def log_error(error_message, error_type="ERROR"):
    """Log error to file"""
    try:
        app_dir = ensure_app_directories()
        log_file = os.path.join(app_dir, 'logs', 'error.log')
        
        timestamp = get_current_timestamp()
        log_entry = f"[{timestamp}] {error_type}: {error_message}\n"
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    except Exception as e:
        print(f"Failed to log error: {e}")

def get_database_path():
    """Get the database file path"""
    app_dir = ensure_app_directories()
    return os.path.join(app_dir, 'warehouse.db')

def get_backup_path(backup_name=None):
    """Get backup file path"""
    app_dir = ensure_app_directories()
    if not backup_name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"warehouse_backup_{timestamp}.db"
    
    return os.path.join(app_dir, 'backups', backup_name)
