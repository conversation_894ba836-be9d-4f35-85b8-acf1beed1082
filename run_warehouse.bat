@echo off
chcp 65001 > nul
title نظام إدارة المستودعات - Warehouse Management System

echo.
echo ========================================
echo    نظام إدارة المستودعات
echo    Warehouse Management System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed on the system
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo Please install Python 3.8 or newer from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM Check if required packages are installed
echo 🔍 فحص المتطلبات...
echo 🔍 Checking requirements...

python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المتطلبات...
    echo 📦 Installing requirements...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo ❌ Failed to install requirements
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo ✅ All requirements satisfied

echo.
echo 🚀 تشغيل البرنامج...
echo 🚀 Starting application...
echo.

REM Run the application
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo ❌ An error occurred while running the application
    echo.
    pause
)

echo.
echo 👋 تم إغلاق البرنامج
echo 👋 Application closed
pause
