import sys
import os
import random
import string
from PyQt6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
                            QMessageBox, QFileDialog, QDialog, QDialogButtonBox,
                            QSpinBox, QDoubleSpinBox, QGroupBox, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon
from database import DatabaseManager
from utils.translations import tr, translator
from ui.styles import get_main_style, COLORS
import barcode
from barcode.writer import ImageWriter

class AddCategoryDialog(QDialog):
    """Dialog for adding new category"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr('add_category'))
        self.setModal(True)
        self.setFixedSize(400, 200)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Arabic name
        layout.addWidget(QLabel(tr('category_name_ar')))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # English name
        layout.addWidget(QLabel(tr('category_name_en')))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Description
        layout.addWidget(QLabel(tr('description')))
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(60)
        layout.addWidget(self.description_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'name_ar': self.name_ar_input.text().strip(),
            'name_en': self.name_en_input.text().strip(),
            'description': self.description_input.toPlainText().strip()
        }

class AddUnitDialog(QDialog):
    """Dialog for adding new unit"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr('add_unit'))
        self.setModal(True)
        self.setFixedSize(400, 180)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Arabic name
        layout.addWidget(QLabel(tr('unit_name_ar')))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # English name
        layout.addWidget(QLabel(tr('unit_name_en')))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Symbol
        layout.addWidget(QLabel(tr('unit_symbol')))
        self.symbol_input = QLineEdit()
        layout.addWidget(self.symbol_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'name_ar': self.name_ar_input.text().strip(),
            'name_en': self.name_en_input.text().strip(),
            'symbol': self.symbol_input.text().strip()
        }

class AddStatusDialog(QDialog):
    """Dialog for adding new status"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr('add_status'))
        self.setModal(True)
        self.setFixedSize(400, 150)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Arabic name
        layout.addWidget(QLabel(tr('status_name_ar')))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # English name
        layout.addWidget(QLabel(tr('status_name_en')))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'name_ar': self.name_ar_input.text().strip(),
            'name_en': self.name_en_input.text().strip()
        }

class ProductsWindow(QWidget):
    """Products management window"""
    
    def __init__(self, db_manager, user_role):
        super().__init__()
        self.db = db_manager
        self.user_role = user_role
        self.current_product_id = None
        self.current_image_path = None
        self.init_ui()
        self.load_data()
        
    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Product form
        left_panel = self.create_product_form()
        splitter.addWidget(left_panel)
        
        # Right panel - Products table
        right_panel = self.create_products_table()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        
    def create_product_form(self):
        """Create the product form panel"""
        form_frame = QFrame()
        form_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel(tr('add_product'))
        title_label.setStyleSheet(f"font-size: 14pt; font-weight: bold; color: {COLORS['accent']}; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Product code
        code_layout = QHBoxLayout()
        code_layout.addWidget(QLabel(tr('product_code')))
        self.code_input = QLineEdit()
        code_layout.addWidget(self.code_input)
        
        # Generate barcode button
        self.generate_barcode_btn = QPushButton(tr('generate_barcode'))
        self.generate_barcode_btn.clicked.connect(self.generate_barcode)
        code_layout.addWidget(self.generate_barcode_btn)
        layout.addLayout(code_layout)
        
        # Product name (Arabic)
        layout.addWidget(QLabel(tr('product_name') + ' (عربي)'))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # Product name (English)
        layout.addWidget(QLabel(tr('product_name') + ' (English)'))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Category
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel(tr('category')))
        self.category_combo = QComboBox()
        category_layout.addWidget(self.category_combo)
        
        # Add category button
        add_category_btn = QPushButton('+')
        add_category_btn.setFixedSize(30, 30)
        add_category_btn.clicked.connect(self.add_category)
        category_layout.addWidget(add_category_btn)
        layout.addLayout(category_layout)
        
        # Unit
        unit_layout = QHBoxLayout()
        unit_layout.addWidget(QLabel(tr('unit')))
        self.unit_combo = QComboBox()
        unit_layout.addWidget(self.unit_combo)
        
        # Add unit button
        add_unit_btn = QPushButton('+')
        add_unit_btn.setFixedSize(30, 30)
        add_unit_btn.clicked.connect(self.add_unit)
        unit_layout.addWidget(add_unit_btn)
        layout.addLayout(unit_layout)
        
        # Status
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel(tr('status')))
        self.status_combo = QComboBox()
        status_layout.addWidget(self.status_combo)
        
        # Add status button
        add_status_btn = QPushButton('+')
        add_status_btn.setFixedSize(30, 30)
        add_status_btn.clicked.connect(self.add_status)
        status_layout.addWidget(add_status_btn)
        layout.addLayout(status_layout)
        
        # Quantity
        layout.addWidget(QLabel(tr('quantity')))
        self.quantity_input = QDoubleSpinBox()
        self.quantity_input.setMaximum(999999.99)
        layout.addWidget(self.quantity_input)
        
        # Price
        layout.addWidget(QLabel(tr('price')))
        self.price_input = QDoubleSpinBox()
        self.price_input.setMaximum(999999.99)
        layout.addWidget(self.price_input)
        
        # Notes
        layout.addWidget(QLabel(tr('notes')))
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        layout.addWidget(self.notes_input)
        
        # Image
        image_layout = QHBoxLayout()
        image_layout.addWidget(QLabel(tr('image')))
        self.image_label = QLabel(tr('no_image', 'No Image'))
        self.image_label.setStyleSheet(f"border: 1px solid {COLORS['border']}; padding: 5px;")
        self.image_label.setFixedSize(100, 100)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        image_layout.addWidget(self.image_label)
        
        self.browse_image_btn = QPushButton(tr('browse_image'))
        self.browse_image_btn.clicked.connect(self.browse_image)
        image_layout.addWidget(self.browse_image_btn)
        layout.addLayout(image_layout)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        self.add_btn = QPushButton(tr('add_product'))
        self.add_btn.setProperty("class", "success")
        self.add_btn.clicked.connect(self.add_product)
        buttons_layout.addWidget(self.add_btn)
        
        self.edit_btn = QPushButton(tr('edit_product'))
        self.edit_btn.setProperty("class", "warning")
        self.edit_btn.clicked.connect(self.edit_product)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton(tr('delete_product'))
        self.delete_btn.setProperty("class", "danger")
        self.delete_btn.clicked.connect(self.delete_product)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)
        
        self.clear_btn = QPushButton(tr('clear_fields'))
        self.clear_btn.clicked.connect(self.clear_fields)
        buttons_layout.addWidget(self.clear_btn)
        
        layout.addLayout(buttons_layout)
        
        form_frame.setLayout(layout)
        return form_frame

    def create_products_table(self):
        """Create the products table panel"""
        table_frame = QFrame()
        table_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                padding: 10px;
            }}
        """)

        layout = QVBoxLayout()

        # Title and search
        header_layout = QHBoxLayout()

        title_label = QLabel(tr('products'))
        title_label.setStyleSheet(f"font-size: 14pt; font-weight: bold; color: {COLORS['accent']};")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Search
        search_label = QLabel(tr('search'))
        header_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr('search_products', 'Search products...'))
        self.search_input.textChanged.connect(self.search_products)
        header_layout.addWidget(self.search_input)

        layout.addLayout(header_layout)

        # Products table
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)

        headers = [
            tr('product_code'),
            tr('product_name'),
            tr('category'),
            tr('unit'),
            tr('quantity'),
            tr('price'),
            tr('status'),
            tr('notes')
        ]

        self.products_table.setHorizontalHeaderLabels(headers)

        # Set column widths
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)

        self.products_table.setColumnWidth(0, 120)  # Code
        self.products_table.setColumnWidth(2, 120)  # Category
        self.products_table.setColumnWidth(3, 80)   # Unit
        self.products_table.setColumnWidth(4, 80)   # Quantity
        self.products_table.setColumnWidth(5, 100)  # Price
        self.products_table.setColumnWidth(6, 100)  # Status

        # Set selection behavior
        self.products_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # Connect selection signal
        self.products_table.itemSelectionChanged.connect(self.on_product_selected)

        layout.addWidget(self.products_table)

        table_frame.setLayout(layout)
        return table_frame

    def load_data(self):
        """Load data from database"""
        self.load_categories()
        self.load_units()
        self.load_status()
        self.load_products()

    def load_categories(self):
        """Load categories into combo box"""
        self.category_combo.clear()
        self.category_combo.addItem("", 0)  # Empty option

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name_ar, name_en FROM categories ORDER BY name_ar")
        categories = cursor.fetchall()

        for category in categories:
            current_lang = translator.language
            name = category['name_ar'] if current_lang == 'ar' else category['name_en']
            self.category_combo.addItem(name, category['id'])

        conn.close()

    def load_units(self):
        """Load units into combo box"""
        self.unit_combo.clear()
        self.unit_combo.addItem("", 0)  # Empty option

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name_ar, name_en, symbol FROM units ORDER BY name_ar")
        units = cursor.fetchall()

        for unit in units:
            current_lang = translator.language
            name = unit['name_ar'] if current_lang == 'ar' else unit['name_en']
            if unit['symbol']:
                name += f" ({unit['symbol']})"
            self.unit_combo.addItem(name, unit['id'])

        conn.close()

    def load_status(self):
        """Load status into combo box"""
        self.status_combo.clear()
        self.status_combo.addItem("", 0)  # Empty option

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name_ar, name_en FROM product_status ORDER BY name_ar")
        statuses = cursor.fetchall()

        for status in statuses:
            current_lang = translator.language
            name = status['name_ar'] if current_lang == 'ar' else status['name_en']
            self.status_combo.addItem(name, status['id'])

        conn.close()

    def load_products(self):
        """Load products into table"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        query = """
            SELECT p.id, p.code, p.name_ar, p.name_en, p.quantity, p.price, p.notes,
                   c.name_ar as category_ar, c.name_en as category_en,
                   u.name_ar as unit_ar, u.name_en as unit_en, u.symbol,
                   s.name_ar as status_ar, s.name_en as status_en
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN units u ON p.unit_id = u.id
            LEFT JOIN product_status s ON p.status_id = s.id
            ORDER BY p.name_ar
        """

        cursor.execute(query)
        products = cursor.fetchall()

        self.products_table.setRowCount(len(products))

        current_lang = translator.language

        for row, product in enumerate(products):
            # Code
            self.products_table.setItem(row, 0, QTableWidgetItem(product['code'] or ''))

            # Name
            name = product['name_ar'] if current_lang == 'ar' else product['name_en']
            self.products_table.setItem(row, 1, QTableWidgetItem(name or ''))

            # Category
            category = product['category_ar'] if current_lang == 'ar' else product['category_en']
            self.products_table.setItem(row, 2, QTableWidgetItem(category or ''))

            # Unit
            unit = product['unit_ar'] if current_lang == 'ar' else product['unit_en']
            if product['symbol']:
                unit += f" ({product['symbol']})"
            self.products_table.setItem(row, 3, QTableWidgetItem(unit or ''))

            # Quantity
            self.products_table.setItem(row, 4, QTableWidgetItem(str(product['quantity'] or 0)))

            # Price
            self.products_table.setItem(row, 5, QTableWidgetItem(str(product['price'] or 0)))

            # Status
            status = product['status_ar'] if current_lang == 'ar' else product['status_en']
            self.products_table.setItem(row, 6, QTableWidgetItem(status or ''))

            # Notes
            self.products_table.setItem(row, 7, QTableWidgetItem(product['notes'] or ''))

            # Store product ID in first column
            self.products_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, product['id'])

        conn.close()

    def generate_barcode(self):
        """Generate random barcode"""
        # Generate random 13-digit barcode
        barcode_number = ''.join(random.choices(string.digits, k=13))
        self.code_input.setText(barcode_number)

    def browse_image(self):
        """Browse for product image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr('select_image', 'Select Image'),
            '',
            'Image Files (*.png *.jpg *.jpeg *.bmp *.gif)'
        )

        if file_path:
            self.current_image_path = file_path
            # Display thumbnail
            pixmap = QPixmap(file_path)
            scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)

    def add_category(self):
        """Add new category"""
        dialog = AddCategoryDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            if data['name_ar'] and data['name_en']:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                try:
                    cursor.execute('''
                        INSERT INTO categories (name_ar, name_en, description)
                        VALUES (?, ?, ?)
                    ''', (data['name_ar'], data['name_en'], data['description']))

                    conn.commit()
                    self.load_categories()

                    QMessageBox.information(self, tr('success'), tr('category_added', 'Category added successfully'))

                except Exception as e:
                    QMessageBox.critical(self, tr('error'), f"Error adding category: {str(e)}")
                finally:
                    conn.close()
            else:
                QMessageBox.warning(self, tr('warning'), tr('fill_required_fields'))

    def add_unit(self):
        """Add new unit"""
        dialog = AddUnitDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            if data['name_ar'] and data['name_en']:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                try:
                    cursor.execute('''
                        INSERT INTO units (name_ar, name_en, symbol)
                        VALUES (?, ?, ?)
                    ''', (data['name_ar'], data['name_en'], data['symbol']))

                    conn.commit()
                    self.load_units()

                    QMessageBox.information(self, tr('success'), tr('unit_added', 'Unit added successfully'))

                except Exception as e:
                    QMessageBox.critical(self, tr('error'), f"Error adding unit: {str(e)}")
                finally:
                    conn.close()
            else:
                QMessageBox.warning(self, tr('warning'), tr('fill_required_fields'))

    def add_status(self):
        """Add new status"""
        dialog = AddStatusDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            if data['name_ar'] and data['name_en']:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                try:
                    cursor.execute('''
                        INSERT INTO product_status (name_ar, name_en)
                        VALUES (?, ?)
                    ''', (data['name_ar'], data['name_en']))

                    conn.commit()
                    self.load_status()

                    QMessageBox.information(self, tr('success'), tr('status_added', 'Status added successfully'))

                except Exception as e:
                    QMessageBox.critical(self, tr('error'), f"Error adding status: {str(e)}")
                finally:
                    conn.close()
            else:
                QMessageBox.warning(self, tr('warning'), tr('fill_required_fields'))

    def add_product(self):
        """Add new product"""
        if not self.validate_product_form():
            return

        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # Check if code already exists
            cursor.execute("SELECT id FROM products WHERE code = ?", (self.code_input.text(),))
            if cursor.fetchone():
                QMessageBox.warning(self, tr('warning'), tr('code_exists', 'Product code already exists'))
                return

            # Get form data
            category_id = self.category_combo.currentData() or None
            unit_id = self.unit_combo.currentData() or None
            status_id = self.status_combo.currentData() or None

            # Copy image to products folder if selected
            image_path = None
            if self.current_image_path:
                products_dir = "images/products"
                os.makedirs(products_dir, exist_ok=True)

                filename = f"{self.code_input.text()}.{self.current_image_path.split('.')[-1]}"
                image_path = os.path.join(products_dir, filename)

                import shutil
                shutil.copy2(self.current_image_path, image_path)

            cursor.execute('''
                INSERT INTO products (code, name_ar, name_en, category_id, unit_id, status_id,
                                    quantity, price, notes, image_path, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (
                self.code_input.text(),
                self.name_ar_input.text(),
                self.name_en_input.text(),
                category_id,
                unit_id,
                status_id,
                self.quantity_input.value(),
                self.price_input.value(),
                self.notes_input.toPlainText(),
                image_path
            ))

            conn.commit()
            self.load_products()
            self.clear_fields()

            QMessageBox.information(self, tr('success'), tr('item_saved'))

        except Exception as e:
            QMessageBox.critical(self, tr('error'), f"Error adding product: {str(e)}")
        finally:
            conn.close()

    def edit_product(self):
        """Edit selected product"""
        if not self.current_product_id:
            QMessageBox.warning(self, tr('warning'), tr('select_item'))
            return

        if not self.validate_product_form():
            return

        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # Check if code already exists for other products
            cursor.execute("SELECT id FROM products WHERE code = ? AND id != ?",
                         (self.code_input.text(), self.current_product_id))
            if cursor.fetchone():
                QMessageBox.warning(self, tr('warning'), tr('code_exists', 'Product code already exists'))
                return

            # Get form data
            category_id = self.category_combo.currentData() or None
            unit_id = self.unit_combo.currentData() or None
            status_id = self.status_combo.currentData() or None

            # Handle image update
            image_path = None
            if self.current_image_path:
                products_dir = "images/products"
                os.makedirs(products_dir, exist_ok=True)

                filename = f"{self.code_input.text()}.{self.current_image_path.split('.')[-1]}"
                image_path = os.path.join(products_dir, filename)

                import shutil
                shutil.copy2(self.current_image_path, image_path)
            else:
                # Keep existing image
                cursor.execute("SELECT image_path FROM products WHERE id = ?", (self.current_product_id,))
                result = cursor.fetchone()
                if result:
                    image_path = result['image_path']

            cursor.execute('''
                UPDATE products SET code = ?, name_ar = ?, name_en = ?, category_id = ?,
                                  unit_id = ?, status_id = ?, quantity = ?, price = ?,
                                  notes = ?, image_path = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                self.code_input.text(),
                self.name_ar_input.text(),
                self.name_en_input.text(),
                category_id,
                unit_id,
                status_id,
                self.quantity_input.value(),
                self.price_input.value(),
                self.notes_input.toPlainText(),
                image_path,
                self.current_product_id
            ))

            conn.commit()
            self.load_products()
            self.clear_fields()

            QMessageBox.information(self, tr('success'), tr('item_saved'))

        except Exception as e:
            QMessageBox.critical(self, tr('error'), f"Error updating product: {str(e)}")
        finally:
            conn.close()

    def delete_product(self):
        """Delete selected product"""
        if not self.current_product_id:
            QMessageBox.warning(self, tr('warning'), tr('select_item'))
            return

        reply = QMessageBox.question(
            self,
            tr('confirm'),
            tr('confirm_delete'),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            try:
                # Delete product image if exists
                cursor.execute("SELECT image_path FROM products WHERE id = ?", (self.current_product_id,))
                result = cursor.fetchone()
                if result and result['image_path'] and os.path.exists(result['image_path']):
                    os.remove(result['image_path'])

                cursor.execute("DELETE FROM products WHERE id = ?", (self.current_product_id,))
                conn.commit()

                self.load_products()
                self.clear_fields()

                QMessageBox.information(self, tr('success'), tr('item_deleted'))

            except Exception as e:
                QMessageBox.critical(self, tr('error'), f"Error deleting product: {str(e)}")
            finally:
                conn.close()

    def clear_fields(self):
        """Clear all form fields"""
        self.current_product_id = None
        self.current_image_path = None

        self.code_input.clear()
        self.name_ar_input.clear()
        self.name_en_input.clear()
        self.category_combo.setCurrentIndex(0)
        self.unit_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.quantity_input.setValue(0)
        self.price_input.setValue(0)
        self.notes_input.clear()

        # Reset image
        self.image_label.clear()
        self.image_label.setText(tr('no_image', 'No Image'))

        # Reset buttons
        self.add_btn.setEnabled(True)
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)

        # Clear table selection
        self.products_table.clearSelection()

    def on_product_selected(self):
        """Handle product selection from table"""
        selected_items = self.products_table.selectedItems()
        if not selected_items:
            self.clear_fields()
            return

        row = selected_items[0].row()
        product_id = self.products_table.item(row, 0).data(Qt.ItemDataRole.UserRole)

        if product_id:
            self.load_product_details(product_id)

    def load_product_details(self, product_id):
        """Load product details into form"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT p.*, c.id as category_id, u.id as unit_id, s.id as status_id
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN units u ON p.unit_id = u.id
                LEFT JOIN product_status s ON p.status_id = s.id
                WHERE p.id = ?
            ''', (product_id,))

            product = cursor.fetchone()

            if product:
                self.current_product_id = product_id

                # Fill form fields
                self.code_input.setText(product['code'] or '')
                self.name_ar_input.setText(product['name_ar'] or '')
                self.name_en_input.setText(product['name_en'] or '')

                # Set combo boxes
                if product['category_id']:
                    for i in range(self.category_combo.count()):
                        if self.category_combo.itemData(i) == product['category_id']:
                            self.category_combo.setCurrentIndex(i)
                            break

                if product['unit_id']:
                    for i in range(self.unit_combo.count()):
                        if self.unit_combo.itemData(i) == product['unit_id']:
                            self.unit_combo.setCurrentIndex(i)
                            break

                if product['status_id']:
                    for i in range(self.status_combo.count()):
                        if self.status_combo.itemData(i) == product['status_id']:
                            self.status_combo.setCurrentIndex(i)
                            break

                self.quantity_input.setValue(product['quantity'] or 0)
                self.price_input.setValue(product['price'] or 0)
                self.notes_input.setPlainText(product['notes'] or '')

                # Load image if exists
                if product['image_path'] and os.path.exists(product['image_path']):
                    pixmap = QPixmap(product['image_path'])
                    scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.image_label.setPixmap(scaled_pixmap)
                else:
                    self.image_label.clear()
                    self.image_label.setText(tr('no_image', 'No Image'))

                # Update buttons
                self.add_btn.setEnabled(False)
                self.edit_btn.setEnabled(True)
                self.delete_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, tr('error'), f"Error loading product: {str(e)}")
        finally:
            conn.close()

    def validate_product_form(self):
        """Validate product form data"""
        if not self.code_input.text().strip():
            QMessageBox.warning(self, tr('warning'), tr('code_required', 'Product code is required'))
            self.code_input.setFocus()
            return False

        if not self.name_ar_input.text().strip():
            QMessageBox.warning(self, tr('warning'), tr('name_ar_required', 'Arabic name is required'))
            self.name_ar_input.setFocus()
            return False

        if not self.name_en_input.text().strip():
            QMessageBox.warning(self, tr('warning'), tr('name_en_required', 'English name is required'))
            self.name_en_input.setFocus()
            return False

        return True

    def search_products(self):
        """Search products based on search input"""
        search_text = self.search_input.text().strip().lower()

        for row in range(self.products_table.rowCount()):
            show_row = False

            # Search in all visible columns
            for col in range(self.products_table.columnCount()):
                item = self.products_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.products_table.setRowHidden(row, not show_row)

if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Apply styles
    app.setStyleSheet(get_main_style())

    # Create database manager
    db = DatabaseManager()

    # Create and show products window
    window = ProductsWindow(db, 'admin')
    window.show()

    sys.exit(app.exec())
