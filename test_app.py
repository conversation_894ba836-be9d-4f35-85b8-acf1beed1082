#!/usr/bin/env python3
"""
Test script for Warehouse Management System
"""

import sys
import os
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from utils.translations import translator, tr
from utils.helpers import *

class TestDatabase(unittest.TestCase):
    """Test database functionality"""
    
    def setUp(self):
        """Set up test database"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db = DatabaseManager(self.test_db_path)
    
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_database_creation(self):
        """Test database creation"""
        self.assertTrue(os.path.exists(self.test_db_path))
    
    def test_default_admin_user(self):
        """Test default admin user creation"""
        user = self.db.authenticate_user('admin', 'admin123')
        self.assertIsNotNone(user)
        self.assertEqual(user['role'], 'admin')
        self.assertEqual(user['username'], 'admin')
    
    def test_invalid_login(self):
        """Test invalid login"""
        user = self.db.authenticate_user('invalid', 'invalid')
        self.assertIsNone(user)
    
    def test_settings(self):
        """Test settings functionality"""
        # Test setting a value
        self.db.set_setting('test_key', 'test_value')
        value = self.db.get_setting('test_key')
        self.assertEqual(value, 'test_value')
        
        # Test getting non-existent setting
        value = self.db.get_setting('non_existent')
        self.assertIsNone(value)

class TestTranslations(unittest.TestCase):
    """Test translation functionality"""
    
    def test_arabic_translation(self):
        """Test Arabic translations"""
        translator.set_language('ar')
        self.assertEqual(tr('login'), 'تسجيل الدخول')
        self.assertEqual(tr('products'), 'المنتجات')
    
    def test_english_translation(self):
        """Test English translations"""
        translator.set_language('en')
        self.assertEqual(tr('login'), 'Login')
        self.assertEqual(tr('products'), 'Products')
    
    def test_fallback_translation(self):
        """Test fallback for missing translations"""
        result = tr('non_existent_key', 'fallback')
        self.assertEqual(result, 'fallback')
    
    def test_language_switching(self):
        """Test language switching"""
        translator.set_language('ar')
        self.assertEqual(tr('save'), 'حفظ')
        
        translator.set_language('en')
        self.assertEqual(tr('save'), 'Save')

class TestHelpers(unittest.TestCase):
    """Test helper functions"""
    
    def test_generate_invoice_number(self):
        """Test invoice number generation"""
        invoice_num = generate_invoice_number('TEST')
        self.assertTrue(invoice_num.startswith('TEST-'))
        self.assertTrue(len(invoice_num) > 10)
    
    def test_generate_barcode(self):
        """Test barcode generation"""
        barcode = generate_barcode(13)
        self.assertEqual(len(barcode), 13)
        self.assertTrue(barcode.isdigit())
    
    def test_format_currency(self):
        """Test currency formatting"""
        formatted = format_currency(1234.56, 'USD')
        self.assertIn('1,234.56', formatted)
        self.assertIn('USD', formatted)
    
    def test_validate_required_fields(self):
        """Test field validation"""
        # Valid fields
        fields = {
            'name': ('John Doe', 'Name'),
            'email': ('<EMAIL>', 'Email')
        }
        is_valid, message = validate_required_fields(fields)
        self.assertTrue(is_valid)
        
        # Invalid fields
        fields = {
            'name': ('', 'Name'),
            'email': ('<EMAIL>', 'Email')
        }
        is_valid, message = validate_required_fields(fields)
        self.assertFalse(is_valid)
        self.assertIn('Name', message)
    
    def test_file_operations(self):
        """Test file operation helpers"""
        # Test directory creation
        test_dir = tempfile.mkdtemp()
        try:
            sub_dir = os.path.join(test_dir, 'subdir')
            self.assertTrue(create_directory(sub_dir))
            self.assertTrue(os.path.exists(sub_dir))
            
            # Test file copying
            source_file = os.path.join(test_dir, 'source.txt')
            dest_file = os.path.join(test_dir, 'dest.txt')
            
            with open(source_file, 'w') as f:
                f.write('test content')
            
            self.assertTrue(copy_file(source_file, dest_file))
            self.assertTrue(os.path.exists(dest_file))
            
            # Test file deletion
            self.assertTrue(delete_file(dest_file))
            self.assertFalse(os.path.exists(dest_file))
            
        finally:
            shutil.rmtree(test_dir)
    
    def test_email_validation(self):
        """Test email validation"""
        self.assertTrue(is_valid_email('<EMAIL>'))
        self.assertTrue(is_valid_email('<EMAIL>'))
        self.assertFalse(is_valid_email('invalid-email'))
        self.assertFalse(is_valid_email('test@'))
        self.assertFalse(is_valid_email('@example.com'))
    
    def test_phone_validation(self):
        """Test phone validation"""
        self.assertTrue(is_valid_phone('1234567890'))
        self.assertTrue(is_valid_phone('******-567-8900'))
        self.assertTrue(is_valid_phone('(*************'))
        self.assertFalse(is_valid_phone('123'))
        self.assertFalse(is_valid_phone('12345678901234567890'))

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def setUp(self):
        """Set up integration test environment"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db = DatabaseManager(self.test_db_path)
    
    def tearDown(self):
        """Clean up integration test environment"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_product_workflow(self):
        """Test complete product management workflow"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # Add a category
            cursor.execute('''
                INSERT INTO categories (name_ar, name_en, description)
                VALUES (?, ?, ?)
            ''', ('إلكترونيات', 'Electronics', 'Electronic devices'))
            
            category_id = cursor.lastrowid
            
            # Add a unit
            cursor.execute('''
                INSERT INTO units (name_ar, name_en, symbol)
                VALUES (?, ?, ?)
            ''', ('قطعة', 'Piece', 'pcs'))
            
            unit_id = cursor.lastrowid
            
            # Add a status
            cursor.execute('''
                INSERT INTO product_status (name_ar, name_en)
                VALUES (?, ?)
            ''', ('متوفر', 'Available'))
            
            status_id = cursor.lastrowid
            
            # Add a product
            cursor.execute('''
                INSERT INTO products (code, name_ar, name_en, category_id, unit_id, status_id, quantity, price)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', ('PRD001', 'لابتوب', 'Laptop', category_id, unit_id, status_id, 10, 2500.00))
            
            product_id = cursor.lastrowid
            
            conn.commit()
            
            # Verify product was added
            cursor.execute('''
                SELECT p.*, c.name_ar as category_name, u.name_ar as unit_name, s.name_ar as status_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN units u ON p.unit_id = u.id
                LEFT JOIN product_status s ON p.status_id = s.id
                WHERE p.id = ?
            ''', (product_id,))
            
            product = cursor.fetchone()
            
            self.assertIsNotNone(product)
            self.assertEqual(product['code'], 'PRD001')
            self.assertEqual(product['name_ar'], 'لابتوب')
            self.assertEqual(product['name_en'], 'Laptop')
            self.assertEqual(product['category_name'], 'إلكترونيات')
            self.assertEqual(product['unit_name'], 'قطعة')
            self.assertEqual(product['status_name'], 'متوفر')
            self.assertEqual(product['quantity'], 10)
            self.assertEqual(product['price'], 2500.00)
            
        finally:
            conn.close()

def run_tests():
    """Run all tests"""
    print("🧪 Running Warehouse Management System Tests")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestDatabase))
    test_suite.addTest(unittest.makeSuite(TestTranslations))
    test_suite.addTest(unittest.makeSuite(TestHelpers))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n✅ All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(run_tests())
