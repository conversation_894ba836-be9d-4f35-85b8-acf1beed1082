import sys
import os
import random
import string
from PyQt6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
                            QMessageBox, QFileDialog, QDialog, QDialogButtonBox,
                            QSpinBox, QDoubleSpinBox, QGroupBox, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon
from database import DatabaseManager
from utils.translations import tr, translator
from ui.styles import get_main_style, COLORS
import barcode
from barcode.writer import ImageWriter

class AddCategoryDialog(QDialog):
    """Dialog for adding new category"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr('add_category'))
        self.setModal(True)
        self.setFixedSize(400, 200)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Arabic name
        layout.addWidget(QLabel(tr('category_name_ar')))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # English name
        layout.addWidget(QLabel(tr('category_name_en')))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Description
        layout.addWidget(QLabel(tr('description')))
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(60)
        layout.addWidget(self.description_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'name_ar': self.name_ar_input.text().strip(),
            'name_en': self.name_en_input.text().strip(),
            'description': self.description_input.toPlainText().strip()
        }

class AddUnitDialog(QDialog):
    """Dialog for adding new unit"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr('add_unit'))
        self.setModal(True)
        self.setFixedSize(400, 180)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Arabic name
        layout.addWidget(QLabel(tr('unit_name_ar')))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # English name
        layout.addWidget(QLabel(tr('unit_name_en')))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Symbol
        layout.addWidget(QLabel(tr('unit_symbol')))
        self.symbol_input = QLineEdit()
        layout.addWidget(self.symbol_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'name_ar': self.name_ar_input.text().strip(),
            'name_en': self.name_en_input.text().strip(),
            'symbol': self.symbol_input.text().strip()
        }

class AddStatusDialog(QDialog):
    """Dialog for adding new status"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr('add_status'))
        self.setModal(True)
        self.setFixedSize(400, 150)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Arabic name
        layout.addWidget(QLabel(tr('status_name_ar')))
        self.name_ar_input = QLineEdit()
        layout.addWidget(self.name_ar_input)
        
        # English name
        layout.addWidget(QLabel(tr('status_name_en')))
        self.name_en_input = QLineEdit()
        layout.addWidget(self.name_en_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'name_ar': self.name_ar_input.text().strip(),
            'name_en': self.name_en_input.text().strip()
        }

class ProductsWindow(QWidget):
    """Products management window"""
    
    def __init__(self, db_manager, user_role):
        super().__init__()
        self.db = db_manager
        self.user_role = user_role
        self.current_product_id = None
        self.current_image_path = None
        self.init_ui()
        self.load_data()

        # Set window properties
        self.setWindowTitle(tr('products') + ' - ' + tr('app_title'))
        self.setMinimumSize(1400, 900)
        
    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Product form
        left_panel = self.create_product_form()
        splitter.addWidget(left_panel)
        
        # Right panel - Products table
        right_panel = self.create_products_table()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions (form panel : table panel)
        splitter.setSizes([450, 950])

        # Set minimum sizes
        left_panel.setMinimumWidth(400)
        right_panel.setMinimumWidth(600)
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        
    def create_product_form(self):
        """Create the product form panel"""
        form_frame = QFrame()
        form_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
        """)

        # Main layout with proper spacing
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # Title
        title_label = QLabel(tr('add_product'))
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: {COLORS['accent']};
            margin-bottom: 15px;
            padding: 10px;
            background-color: {COLORS['card']};
            border-radius: 6px;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # Create form using grid layout for better organization
        form_layout = QGridLayout()
        form_layout.setSpacing(10)
        form_layout.setColumnStretch(1, 1)  # Make input columns expandable

        row = 0

        # Product code row
        code_label = QLabel(tr('product_code') + ':')
        code_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(code_label, row, 0)

        code_widget = QWidget()
        code_layout = QHBoxLayout(code_widget)
        code_layout.setContentsMargins(0, 0, 0, 0)

        self.code_input = QLineEdit()
        self.code_input.setMinimumHeight(35)
        code_layout.addWidget(self.code_input, 2)

        self.generate_barcode_btn = QPushButton(tr('generate_barcode'))
        self.generate_barcode_btn.setMinimumHeight(35)
        self.generate_barcode_btn.clicked.connect(self.generate_barcode)
        code_layout.addWidget(self.generate_barcode_btn, 1)

        form_layout.addWidget(code_widget, row, 1)
        row += 1

        # Product name (Arabic)
        name_ar_label = QLabel(tr('product_name') + ' (عربي):')
        name_ar_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(name_ar_label, row, 0)

        self.name_ar_input = QLineEdit()
        self.name_ar_input.setMinimumHeight(35)
        form_layout.addWidget(self.name_ar_input, row, 1)
        row += 1

        # Product name (English)
        name_en_label = QLabel(tr('product_name') + ' (English):')
        name_en_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(name_en_label, row, 0)

        self.name_en_input = QLineEdit()
        self.name_en_input.setMinimumHeight(35)
        form_layout.addWidget(self.name_en_input, row, 1)
        row += 1

        # Category row
        category_label = QLabel(tr('category') + ':')
        category_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(category_label, row, 0)

        category_widget = QWidget()
        category_layout = QHBoxLayout(category_widget)
        category_layout.setContentsMargins(0, 0, 0, 0)

        self.category_combo = QComboBox()
        self.category_combo.setMinimumHeight(35)
        category_layout.addWidget(self.category_combo, 3)

        add_category_btn = QPushButton('+')
        add_category_btn.setFixedSize(35, 35)
        add_category_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['success']};
                color: white;
                font-weight: bold;
                font-size: 14pt;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
        """)
        add_category_btn.clicked.connect(self.add_category)
        category_layout.addWidget(add_category_btn)

        form_layout.addWidget(category_widget, row, 1)
        row += 1

        # Unit row
        unit_label = QLabel(tr('unit') + ':')
        unit_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(unit_label, row, 0)

        unit_widget = QWidget()
        unit_layout = QHBoxLayout(unit_widget)
        unit_layout.setContentsMargins(0, 0, 0, 0)

        self.unit_combo = QComboBox()
        self.unit_combo.setMinimumHeight(35)
        unit_layout.addWidget(self.unit_combo, 3)

        add_unit_btn = QPushButton('+')
        add_unit_btn.setFixedSize(35, 35)
        add_unit_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['success']};
                color: white;
                font-weight: bold;
                font-size: 14pt;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
        """)
        add_unit_btn.clicked.connect(self.add_unit)
        unit_layout.addWidget(add_unit_btn)

        form_layout.addWidget(unit_widget, row, 1)
        row += 1

        # Status row
        status_label = QLabel(tr('status') + ':')
        status_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(status_label, row, 0)

        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)

        self.status_combo = QComboBox()
        self.status_combo.setMinimumHeight(35)
        status_layout.addWidget(self.status_combo, 3)

        add_status_btn = QPushButton('+')
        add_status_btn.setFixedSize(35, 35)
        add_status_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['success']};
                color: white;
                font-weight: bold;
                font-size: 14pt;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
        """)
        add_status_btn.clicked.connect(self.add_status)
        status_layout.addWidget(add_status_btn)

        form_layout.addWidget(status_widget, row, 1)
        row += 1

        # Quantity and Price in same row
        qty_price_widget = QWidget()
        qty_price_layout = QHBoxLayout(qty_price_widget)
        qty_price_layout.setSpacing(15)

        # Quantity
        qty_group = QWidget()
        qty_group_layout = QVBoxLayout(qty_group)
        qty_group_layout.setContentsMargins(0, 0, 0, 0)

        qty_label = QLabel(tr('quantity') + ':')
        qty_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        qty_group_layout.addWidget(qty_label)

        self.quantity_input = QDoubleSpinBox()
        self.quantity_input.setMaximum(999999.99)
        self.quantity_input.setMinimumHeight(35)
        self.quantity_input.setDecimals(2)
        qty_group_layout.addWidget(self.quantity_input)

        qty_price_layout.addWidget(qty_group)

        # Price
        price_group = QWidget()
        price_group_layout = QVBoxLayout(price_group)
        price_group_layout.setContentsMargins(0, 0, 0, 0)

        price_label = QLabel(tr('price') + ':')
        price_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        price_group_layout.addWidget(price_label)

        self.price_input = QDoubleSpinBox()
        self.price_input.setMaximum(999999.99)
        self.price_input.setMinimumHeight(35)
        self.price_input.setDecimals(2)
        price_group_layout.addWidget(self.price_input)

        qty_price_layout.addWidget(price_group)

        form_layout.addWidget(qty_price_widget, row, 0, 1, 2)
        row += 1

        # Notes
        notes_label = QLabel(tr('notes') + ':')
        notes_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        form_layout.addWidget(notes_label, row, 0, Qt.AlignmentFlag.AlignTop)

        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setMinimumHeight(80)
        form_layout.addWidget(self.notes_input, row, 1)
        row += 1

        # Image section
        image_group = QGroupBox(tr('image'))
        image_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                color: {COLORS['text_primary']};
                border: 2px solid {COLORS['border']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {COLORS['accent']};
            }}
        """)

        image_layout = QHBoxLayout(image_group)

        self.image_label = QLabel(tr('no_image', 'No Image'))
        self.image_label.setStyleSheet(f"""
            border: 2px dashed {COLORS['border']};
            padding: 10px;
            background-color: {COLORS['card']};
            border-radius: 6px;
            color: {COLORS['text_secondary']};
        """)
        self.image_label.setFixedSize(120, 120)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setScaledContents(True)
        image_layout.addWidget(self.image_label)

        self.browse_image_btn = QPushButton(tr('browse_image'))
        self.browse_image_btn.setMinimumHeight(40)
        self.browse_image_btn.clicked.connect(self.browse_image)
        image_layout.addWidget(self.browse_image_btn)

        form_layout.addWidget(image_group, row, 0, 1, 2)
        row += 1

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add spacer
        main_layout.addStretch()

        # Buttons section
        buttons_group = QGroupBox(tr('actions', 'Actions'))
        buttons_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                color: {COLORS['text_primary']};
                border: 2px solid {COLORS['border']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {COLORS['accent']};
            }}
        """)

        buttons_layout = QGridLayout(buttons_group)
        buttons_layout.setSpacing(10)

        self.add_btn = QPushButton(tr('add_product'))
        self.add_btn.setProperty("class", "success")
        self.add_btn.setMinimumHeight(40)
        self.add_btn.clicked.connect(self.add_product)
        buttons_layout.addWidget(self.add_btn, 0, 0)

        self.edit_btn = QPushButton(tr('edit_product'))
        self.edit_btn.setProperty("class", "warning")
        self.edit_btn.setMinimumHeight(40)
        self.edit_btn.clicked.connect(self.edit_product)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn, 0, 1)

        self.delete_btn = QPushButton(tr('delete_product'))
        self.delete_btn.setProperty("class", "danger")
        self.delete_btn.setMinimumHeight(40)
        self.delete_btn.clicked.connect(self.delete_product)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn, 1, 0)

        self.clear_btn = QPushButton(tr('clear_fields'))
        self.clear_btn.setMinimumHeight(40)
        self.clear_btn.clicked.connect(self.clear_fields)
        buttons_layout.addWidget(self.clear_btn, 1, 1)

        main_layout.addWidget(buttons_group)

        form_frame.setLayout(main_layout)
        return form_frame

    def create_products_table(self):
        """Create the products table panel"""
        table_frame = QFrame()
        table_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)

        # Title section
        title_section = QWidget()
        title_section.setStyleSheet(f"""
            QWidget {{
                background-color: {COLORS['card']};
                border-radius: 6px;
                padding: 10px;
            }}
        """)

        title_layout = QHBoxLayout(title_section)
        title_layout.setContentsMargins(15, 10, 15, 10)

        title_label = QLabel(tr('products'))
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: {COLORS['accent']};
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # Search section
        search_widget = QWidget()
        search_layout = QHBoxLayout(search_widget)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)

        search_label = QLabel(tr('search') + ':')
        search_label.setStyleSheet(f"font-weight: bold; color: {COLORS['text_primary']};")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr('search_products', 'Search products...'))
        self.search_input.setMinimumHeight(35)
        self.search_input.setMinimumWidth(250)
        self.search_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 8px 12px;
                border: 2px solid {COLORS['border']};
                border-radius: 6px;
                background-color: {COLORS['background']};
                color: {COLORS['text_primary']};
                font-size: 11pt;
            }}
            QLineEdit:focus {{
                border-color: {COLORS['accent']};
            }}
        """)
        self.search_input.textChanged.connect(self.search_products)
        search_layout.addWidget(self.search_input)

        title_layout.addWidget(search_widget)

        layout.addWidget(title_section)

        # Table container
        table_container = QWidget()
        table_container.setStyleSheet(f"""
            QWidget {{
                background-color: {COLORS['card']};
                border-radius: 8px;
                padding: 10px;
            }}
        """)

        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(10, 10, 10, 10)

        # Products table
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)

        headers = [
            tr('product_code'),
            tr('product_name'),
            tr('category'),
            tr('unit'),
            tr('quantity'),
            tr('price'),
            tr('status'),
            tr('notes')
        ]

        self.products_table.setHorizontalHeaderLabels(headers)

        # Enhanced table styling
        self.products_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {COLORS['background']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 6px;
                gridline-color: {COLORS['border']};
                selection-background-color: {COLORS['selected']};
                font-size: 10pt;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {COLORS['border']};
                border-right: 1px solid {COLORS['border']};
            }}
            QTableWidget::item:selected {{
                background-color: {COLORS['selected']};
                color: white;
            }}
            QTableWidget::item:hover {{
                background-color: {COLORS['hover']};
            }}
            QHeaderView::section {{
                background-color: {COLORS['primary']};
                color: {COLORS['text_primary']};
                padding: 12px 8px;
                border: none;
                border-right: 1px solid {COLORS['border']};
                border-bottom: 2px solid {COLORS['accent']};
                font-weight: bold;
                font-size: 11pt;
            }}
            QHeaderView::section:hover {{
                background-color: {COLORS['secondary']};
            }}
        """)

        # Set column widths and resize modes
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)    # Code
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Name
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)    # Category
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)    # Unit
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)    # Quantity
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)    # Price
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)    # Status
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)  # Notes

        self.products_table.setColumnWidth(0, 130)  # Code
        self.products_table.setColumnWidth(2, 130)  # Category
        self.products_table.setColumnWidth(3, 90)   # Unit
        self.products_table.setColumnWidth(4, 90)   # Quantity
        self.products_table.setColumnWidth(5, 110)  # Price
        self.products_table.setColumnWidth(6, 110)  # Status

        # Set table properties
        self.products_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSortingEnabled(True)
        self.products_table.verticalHeader().setVisible(False)

        # Set minimum row height
        self.products_table.verticalHeader().setDefaultSectionSize(40)

        # Connect selection signal
        self.products_table.itemSelectionChanged.connect(self.on_product_selected)

        table_layout.addWidget(self.products_table)

        # Table info footer
        self.table_info_label = QLabel()
        self.table_info_label.setStyleSheet(f"""
            color: {COLORS['text_secondary']};
            font-size: 9pt;
            padding: 5px;
        """)
        self.table_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        table_layout.addWidget(self.table_info_label)

        layout.addWidget(table_container)

        table_frame.setLayout(layout)
        return table_frame

    def load_data(self):
        """Load data from database"""
        self.load_categories()
        self.load_units()
        self.load_status()
        self.load_products()

    def load_categories(self):
        """Load categories into combo box"""
        self.category_combo.clear()
        self.category_combo.addItem("", 0)  # Empty option

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name_ar, name_en FROM categories ORDER BY name_ar")
        categories = cursor.fetchall()

        for category in categories:
            current_lang = translator.language
            name = category['name_ar'] if current_lang == 'ar' else category['name_en']
            self.category_combo.addItem(name, category['id'])

        conn.close()

    def load_units(self):
        """Load units into combo box"""
        self.unit_combo.clear()
        self.unit_combo.addItem("", 0)  # Empty option

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name_ar, name_en, symbol FROM units ORDER BY name_ar")
        units = cursor.fetchall()

        for unit in units:
            current_lang = translator.language
            name = unit['name_ar'] if current_lang == 'ar' else unit['name_en']
            if unit['symbol']:
                name += f" ({unit['symbol']})"
            self.unit_combo.addItem(name, unit['id'])

        conn.close()

    def load_status(self):
        """Load status into combo box"""
        self.status_combo.clear()
        self.status_combo.addItem("", 0)  # Empty option

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name_ar, name_en FROM product_status ORDER BY name_ar")
        statuses = cursor.fetchall()

        for status in statuses:
            current_lang = translator.language
            name = status['name_ar'] if current_lang == 'ar' else status['name_en']
            self.status_combo.addItem(name, status['id'])

        conn.close()

    def load_products(self):
        """Load products into table"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        query = """
            SELECT p.id, p.code, p.name_ar, p.name_en, p.quantity, p.price, p.notes,
                   c.name_ar as category_ar, c.name_en as category_en,
                   u.name_ar as unit_ar, u.name_en as unit_en, u.symbol,
                   s.name_ar as status_ar, s.name_en as status_en
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN units u ON p.unit_id = u.id
            LEFT JOIN product_status s ON p.status_id = s.id
            ORDER BY p.name_ar
        """

        cursor.execute(query)
        products = cursor.fetchall()

        self.products_table.setRowCount(len(products))

        current_lang = translator.language

        for row, product in enumerate(products):
            # Code
            self.products_table.setItem(row, 0, QTableWidgetItem(product['code'] or ''))

            # Name
            name = product['name_ar'] if current_lang == 'ar' else product['name_en']
            self.products_table.setItem(row, 1, QTableWidgetItem(name or ''))

            # Category
            category = product['category_ar'] if current_lang == 'ar' else product['category_en']
            self.products_table.setItem(row, 2, QTableWidgetItem(category or ''))

            # Unit
            unit = product['unit_ar'] if current_lang == 'ar' else product['unit_en']
            if product['symbol']:
                unit += f" ({product['symbol']})"
            self.products_table.setItem(row, 3, QTableWidgetItem(unit or ''))

            # Quantity
            self.products_table.setItem(row, 4, QTableWidgetItem(str(product['quantity'] or 0)))

            # Price
            self.products_table.setItem(row, 5, QTableWidgetItem(str(product['price'] or 0)))

            # Status
            status = product['status_ar'] if current_lang == 'ar' else product['status_en']
            self.products_table.setItem(row, 6, QTableWidgetItem(status or ''))

            # Notes
            self.products_table.setItem(row, 7, QTableWidgetItem(product['notes'] or ''))

            # Store product ID in first column
            self.products_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, product['id'])

        conn.close()

        # Update table info
        self.update_table_info()

    def generate_barcode(self):
        """Generate random barcode"""
        # Generate random 13-digit barcode
        barcode_number = ''.join(random.choices(string.digits, k=13))
        self.code_input.setText(barcode_number)

    def browse_image(self):
        """Browse for product image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr('select_image', 'Select Image'),
            '',
            'Image Files (*.png *.jpg *.jpeg *.bmp *.gif)'
        )

        if file_path:
            self.current_image_path = file_path
            # Display thumbnail
            pixmap = QPixmap(file_path)
            scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)

    def add_category(self):
        """Add new category"""
        dialog = AddCategoryDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            if data['name_ar'] and data['name_en']:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                try:
                    cursor.execute('''
                        INSERT INTO categories (name_ar, name_en, description)
                        VALUES (?, ?, ?)
                    ''', (data['name_ar'], data['name_en'], data['description']))

                    conn.commit()
                    self.load_categories()

                    QMessageBox.information(self, tr('success'), tr('category_added', 'Category added successfully'))

                except Exception as e:
                    QMessageBox.critical(self, tr('error'), f"Error adding category: {str(e)}")
                finally:
                    conn.close()
            else:
                QMessageBox.warning(self, tr('warning'), tr('fill_required_fields'))

    def add_unit(self):
        """Add new unit"""
        dialog = AddUnitDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            if data['name_ar'] and data['name_en']:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                try:
                    cursor.execute('''
                        INSERT INTO units (name_ar, name_en, symbol)
                        VALUES (?, ?, ?)
                    ''', (data['name_ar'], data['name_en'], data['symbol']))

                    conn.commit()
                    self.load_units()

                    QMessageBox.information(self, tr('success'), tr('unit_added', 'Unit added successfully'))

                except Exception as e:
                    QMessageBox.critical(self, tr('error'), f"Error adding unit: {str(e)}")
                finally:
                    conn.close()
            else:
                QMessageBox.warning(self, tr('warning'), tr('fill_required_fields'))

    def add_status(self):
        """Add new status"""
        dialog = AddStatusDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            if data['name_ar'] and data['name_en']:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                try:
                    cursor.execute('''
                        INSERT INTO product_status (name_ar, name_en)
                        VALUES (?, ?)
                    ''', (data['name_ar'], data['name_en']))

                    conn.commit()
                    self.load_status()

                    QMessageBox.information(self, tr('success'), tr('status_added', 'Status added successfully'))

                except Exception as e:
                    QMessageBox.critical(self, tr('error'), f"Error adding status: {str(e)}")
                finally:
                    conn.close()
            else:
                QMessageBox.warning(self, tr('warning'), tr('fill_required_fields'))

    def add_product(self):
        """Add new product"""
        if not self.validate_product_form():
            return

        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # Check if code already exists
            cursor.execute("SELECT id FROM products WHERE code = ?", (self.code_input.text(),))
            if cursor.fetchone():
                QMessageBox.warning(self, tr('warning'), tr('code_exists', 'Product code already exists'))
                return

            # Get form data
            category_id = self.category_combo.currentData() or None
            unit_id = self.unit_combo.currentData() or None
            status_id = self.status_combo.currentData() or None

            # Copy image to products folder if selected
            image_path = None
            if self.current_image_path:
                products_dir = "images/products"
                os.makedirs(products_dir, exist_ok=True)

                filename = f"{self.code_input.text()}.{self.current_image_path.split('.')[-1]}"
                image_path = os.path.join(products_dir, filename)

                import shutil
                shutil.copy2(self.current_image_path, image_path)

            cursor.execute('''
                INSERT INTO products (code, name_ar, name_en, category_id, unit_id, status_id,
                                    quantity, price, notes, image_path, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (
                self.code_input.text(),
                self.name_ar_input.text(),
                self.name_en_input.text(),
                category_id,
                unit_id,
                status_id,
                self.quantity_input.value(),
                self.price_input.value(),
                self.notes_input.toPlainText(),
                image_path
            ))

            conn.commit()
            self.load_products()
            self.clear_fields()

            QMessageBox.information(self, tr('success'), tr('item_saved'))

        except Exception as e:
            QMessageBox.critical(self, tr('error'), f"Error adding product: {str(e)}")
        finally:
            conn.close()

    def edit_product(self):
        """Edit selected product"""
        if not self.current_product_id:
            QMessageBox.warning(self, tr('warning'), tr('select_item'))
            return

        if not self.validate_product_form():
            return

        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # Check if code already exists for other products
            cursor.execute("SELECT id FROM products WHERE code = ? AND id != ?",
                         (self.code_input.text(), self.current_product_id))
            if cursor.fetchone():
                QMessageBox.warning(self, tr('warning'), tr('code_exists', 'Product code already exists'))
                return

            # Get form data
            category_id = self.category_combo.currentData() or None
            unit_id = self.unit_combo.currentData() or None
            status_id = self.status_combo.currentData() or None

            # Handle image update
            image_path = None
            if self.current_image_path:
                products_dir = "images/products"
                os.makedirs(products_dir, exist_ok=True)

                filename = f"{self.code_input.text()}.{self.current_image_path.split('.')[-1]}"
                image_path = os.path.join(products_dir, filename)

                import shutil
                shutil.copy2(self.current_image_path, image_path)
            else:
                # Keep existing image
                cursor.execute("SELECT image_path FROM products WHERE id = ?", (self.current_product_id,))
                result = cursor.fetchone()
                if result:
                    image_path = result['image_path']

            cursor.execute('''
                UPDATE products SET code = ?, name_ar = ?, name_en = ?, category_id = ?,
                                  unit_id = ?, status_id = ?, quantity = ?, price = ?,
                                  notes = ?, image_path = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                self.code_input.text(),
                self.name_ar_input.text(),
                self.name_en_input.text(),
                category_id,
                unit_id,
                status_id,
                self.quantity_input.value(),
                self.price_input.value(),
                self.notes_input.toPlainText(),
                image_path,
                self.current_product_id
            ))

            conn.commit()
            self.load_products()
            self.clear_fields()

            QMessageBox.information(self, tr('success'), tr('item_saved'))

        except Exception as e:
            QMessageBox.critical(self, tr('error'), f"Error updating product: {str(e)}")
        finally:
            conn.close()

    def delete_product(self):
        """Delete selected product"""
        if not self.current_product_id:
            QMessageBox.warning(self, tr('warning'), tr('select_item'))
            return

        reply = QMessageBox.question(
            self,
            tr('confirm'),
            tr('confirm_delete'),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            try:
                # Delete product image if exists
                cursor.execute("SELECT image_path FROM products WHERE id = ?", (self.current_product_id,))
                result = cursor.fetchone()
                if result and result['image_path'] and os.path.exists(result['image_path']):
                    os.remove(result['image_path'])

                cursor.execute("DELETE FROM products WHERE id = ?", (self.current_product_id,))
                conn.commit()

                self.load_products()
                self.clear_fields()

                QMessageBox.information(self, tr('success'), tr('item_deleted'))

            except Exception as e:
                QMessageBox.critical(self, tr('error'), f"Error deleting product: {str(e)}")
            finally:
                conn.close()

    def clear_fields(self):
        """Clear all form fields"""
        self.current_product_id = None
        self.current_image_path = None

        self.code_input.clear()
        self.name_ar_input.clear()
        self.name_en_input.clear()
        self.category_combo.setCurrentIndex(0)
        self.unit_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.quantity_input.setValue(0)
        self.price_input.setValue(0)
        self.notes_input.clear()

        # Reset image
        self.image_label.clear()
        self.image_label.setText(tr('no_image', 'No Image'))

        # Reset buttons
        self.add_btn.setEnabled(True)
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)

        # Clear table selection
        self.products_table.clearSelection()

    def on_product_selected(self):
        """Handle product selection from table"""
        selected_items = self.products_table.selectedItems()
        if not selected_items:
            self.clear_fields()
            return

        row = selected_items[0].row()
        product_id = self.products_table.item(row, 0).data(Qt.ItemDataRole.UserRole)

        if product_id:
            self.load_product_details(product_id)

    def load_product_details(self, product_id):
        """Load product details into form"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT p.*, c.id as category_id, u.id as unit_id, s.id as status_id
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN units u ON p.unit_id = u.id
                LEFT JOIN product_status s ON p.status_id = s.id
                WHERE p.id = ?
            ''', (product_id,))

            product = cursor.fetchone()

            if product:
                self.current_product_id = product_id

                # Fill form fields
                self.code_input.setText(product['code'] or '')
                self.name_ar_input.setText(product['name_ar'] or '')
                self.name_en_input.setText(product['name_en'] or '')

                # Set combo boxes
                if product['category_id']:
                    for i in range(self.category_combo.count()):
                        if self.category_combo.itemData(i) == product['category_id']:
                            self.category_combo.setCurrentIndex(i)
                            break

                if product['unit_id']:
                    for i in range(self.unit_combo.count()):
                        if self.unit_combo.itemData(i) == product['unit_id']:
                            self.unit_combo.setCurrentIndex(i)
                            break

                if product['status_id']:
                    for i in range(self.status_combo.count()):
                        if self.status_combo.itemData(i) == product['status_id']:
                            self.status_combo.setCurrentIndex(i)
                            break

                self.quantity_input.setValue(product['quantity'] or 0)
                self.price_input.setValue(product['price'] or 0)
                self.notes_input.setPlainText(product['notes'] or '')

                # Load image if exists
                if product['image_path'] and os.path.exists(product['image_path']):
                    pixmap = QPixmap(product['image_path'])
                    scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.image_label.setPixmap(scaled_pixmap)
                else:
                    self.image_label.clear()
                    self.image_label.setText(tr('no_image', 'No Image'))

                # Update buttons
                self.add_btn.setEnabled(False)
                self.edit_btn.setEnabled(True)
                self.delete_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, tr('error'), f"Error loading product: {str(e)}")
        finally:
            conn.close()

    def validate_product_form(self):
        """Validate product form data"""
        if not self.code_input.text().strip():
            QMessageBox.warning(self, tr('warning'), tr('code_required', 'Product code is required'))
            self.code_input.setFocus()
            return False

        if not self.name_ar_input.text().strip():
            QMessageBox.warning(self, tr('warning'), tr('name_ar_required', 'Arabic name is required'))
            self.name_ar_input.setFocus()
            return False

        if not self.name_en_input.text().strip():
            QMessageBox.warning(self, tr('warning'), tr('name_en_required', 'English name is required'))
            self.name_en_input.setFocus()
            return False

        return True

    def search_products(self):
        """Search products based on search input"""
        search_text = self.search_input.text().strip().lower()

        for row in range(self.products_table.rowCount()):
            show_row = False

            # Search in all visible columns
            for col in range(self.products_table.columnCount()):
                item = self.products_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.products_table.setRowHidden(row, not show_row)

        # Update table info after search
        self.update_table_info()

    def update_table_info(self):
        """Update table information footer"""
        total_rows = self.products_table.rowCount()
        visible_rows = 0

        for row in range(total_rows):
            if not self.products_table.isRowHidden(row):
                visible_rows += 1

        if visible_rows == total_rows:
            info_text = f"إجمالي المنتجات: {total_rows} | Total Products: {total_rows}"
        else:
            info_text = f"عرض {visible_rows} من {total_rows} منتج | Showing {visible_rows} of {total_rows} products"

        self.table_info_label.setText(info_text)

if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Apply styles
    app.setStyleSheet(get_main_style())

    # Create database manager
    db = DatabaseManager()

    # Create and show products window
    window = ProductsWindow(db, 'admin')
    window.show()

    sys.exit(app.exec())
