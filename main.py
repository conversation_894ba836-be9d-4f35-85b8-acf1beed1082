#!/usr/bin/env python3
"""
Warehouse Management System
Main application entry point
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt6.QtCore import Qt, QTranslator, QLocale
from PyQt6.QtGui import QIcon, QFont

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from ui.styles import get_main_style
from utils.translations import translator, tr

class WarehouseApp:
    """Main application class"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.db = DatabaseManager()
        self.current_user = None
        self.main_window = None
        
        self.setup_application()
        
    def setup_application(self):
        """Setup application properties and styles"""
        
        # Set application properties
        self.app.setApplicationName(tr('app_title'))
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("Warehouse Management")
        self.app.setOrganizationDomain("warehouse.local")
        
        # Set application icon (if available)
        try:
            icon_path = "assets/icon.png"
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        # Set default font
        font = QFont("Segoe UI", 10)
        self.app.setFont(font)
        
        # Apply global stylesheet
        self.app.setStyleSheet(get_main_style())
        
        # Set language from database settings
        language = self.db.get_setting('language') or 'ar'
        translator.set_language(language)
        
        # Configure for RTL languages
        if language == 'ar':
            self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        else:
            self.app.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
    
    def show_login(self):
        """Show login window"""
        login_window = LoginWindow()
        login_window.login_successful.connect(self.on_login_success)
        
        # Center the login window
        login_window.show()
        
        return login_window.exec()
    
    def on_login_success(self, user_data):
        """Handle successful login"""
        self.current_user = user_data
        self.show_main_window()
    
    def show_main_window(self):
        """Show main application window"""
        if self.current_user:
            self.main_window = MainWindow(self.current_user)
            self.main_window.show()
    
    def run(self):
        """Run the application"""
        try:
            # Show login window
            if self.show_login() == QDialog.DialogCode.Accepted:
                # Login successful, main window is already shown
                return self.app.exec()
            else:
                # Login cancelled or failed
                return 0
                
        except Exception as e:
            # Show error message
            QMessageBox.critical(
                None,
                tr('error'),
                f"Application error: {str(e)}"
            )
            return 1

def main():
    """Main entry point"""
    
    # Enable high DPI scaling
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )
    
    # Create and run application
    app = WarehouseApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
