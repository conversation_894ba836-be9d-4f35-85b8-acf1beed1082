#!/usr/bin/env python3
"""
Test script for Products UI
اختبار واجهة المنتجات
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from ui.products_window import ProductsWindow
from ui.styles import get_main_style
from utils.translations import translator

def main():
    """Test the products window"""
    
    # Create application
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Products Test")
    app.setApplicationVersion("1.0")
    
    # Apply styles
    app.setStyleSheet(get_main_style())
    
    # Set language
    translator.set_language('ar')
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    # Create database manager
    db = DatabaseManager()
    
    # Create products window
    products_window = ProductsWindow(db, 'admin')
    
    # Show window
    products_window.show()
    
    print("🚀 Products window opened successfully!")
    print("📝 Test the following features:")
    print("   ✅ Add new product")
    print("   ✅ Generate barcode")
    print("   ✅ Add category/unit/status")
    print("   ✅ Upload product image")
    print("   ✅ Search products")
    print("   ✅ Edit/Delete products")
    print("   ✅ Clear form fields")
    print()
    print("🔍 Check the layout:")
    print("   ✅ Form panel on the left")
    print("   ✅ Products table on the right")
    print("   ✅ Proper spacing and alignment")
    print("   ✅ Responsive design")
    print()
    print("🎨 Verify styling:")
    print("   ✅ Dark theme colors")
    print("   ✅ Modern button styles")
    print("   ✅ Clear typography")
    print("   ✅ Proper borders and shadows")
    
    # Run application
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
