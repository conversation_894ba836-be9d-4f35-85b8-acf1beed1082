#!/bin/bash

# Warehouse Management System Launcher Script
# نظام إدارة المستودعات - سكريبت التشغيل

echo ""
echo "========================================"
echo "    نظام إدارة المستودعات"
echo "    Warehouse Management System"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🔍${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

# Check if Python is installed
print_status "فحص Python... / Checking Python..."

if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        print_error "Python غير مثبت على النظام"
        print_error "Python is not installed on the system"
        echo ""
        echo "يرجى تثبيت Python 3.8 أو أحدث من:"
        echo "Please install Python 3.8 or newer from:"
        echo "https://www.python.org/downloads/"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
print_success "Python $PYTHON_VERSION موجود / Python $PYTHON_VERSION found"

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        print_error "pip غير متوفر / pip not available"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

# Check if virtual environment should be used
if [ -d "venv" ]; then
    print_status "تفعيل البيئة الافتراضية... / Activating virtual environment..."
    source venv/bin/activate
    print_success "تم تفعيل البيئة الافتراضية / Virtual environment activated"
fi

# Check if required packages are installed
print_status "فحص المتطلبات... / Checking requirements..."

$PYTHON_CMD -c "import PyQt6" 2>/dev/null
if [ $? -ne 0 ]; then
    print_warning "تثبيت المتطلبات... / Installing requirements..."
    $PIP_CMD install -r requirements.txt
    
    if [ $? -ne 0 ]; then
        print_error "فشل في تثبيت المتطلبات / Failed to install requirements"
        exit 1
    fi
fi

print_success "جميع المتطلبات متوفرة / All requirements satisfied"

echo ""
print_status "تشغيل البرنامج... / Starting application..."
echo ""

# Make sure we're in the correct directory
cd "$(dirname "$0")"

# Run the application
$PYTHON_CMD main.py

EXIT_CODE=$?

echo ""
if [ $EXIT_CODE -eq 0 ]; then
    print_success "تم إغلاق البرنامج بنجاح / Application closed successfully"
else
    print_error "حدث خطأ أثناء تشغيل البرنامج / An error occurred while running the application"
    echo "Exit code: $EXIT_CODE"
fi

echo ""
echo "👋 وداعاً / Goodbye"
