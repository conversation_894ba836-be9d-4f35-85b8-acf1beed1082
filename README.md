# نظام إدارة المستودعات - Warehouse Management System

نظام متكامل لإدارة فواتير المستودعات مبني باستخدام Python و PyQt6 مع قاعدة بيانات SQLite.

## المميزات الرئيسية

### 🔐 نظام المستخدمين والصلاحيات
- تسجيل دخول آمن مع تشفير كلمات المرور
- ثلاثة مستويات صلاحيات:
  - **مدير**: صلاحية كاملة على جميع الوظائف
  - **موظف مخزن**: إدارة المنتجات والفواتير
  - **مشاهد فقط**: عرض البيانات والتقارير فقط

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- كود المنتج (يدوي أو توليد باركود تلقائي)
- دعم الأسماء بالعربية والإنجليزية
- تصنيفات ووحدات قياس قابلة للتخصيص
- حالات المنتجات القابلة للتخصيص
- رفع صور المنتجات
- بحث متقدم في المنتجات

### 🧾 إدارة الفواتير
- **فواتير الشراء**: تسجيل المشتريات وتحديث المخزون تلقائياً
- **فواتير المبيعات**: تسجيل المبيعات وتقليل المخزون تلقائياً
- ترقيم تلقائي للفواتير
- ربط الفواتير بالموردين والعملاء

### 📊 التقارير
- تقارير المشتريات حسب التاريخ أو المورد
- تقارير المبيعات حسب التاريخ أو العميل
- تقرير المخزون الحالي
- تصدير التقارير إلى PDF و Excel

### ⚙️ الإعدادات
- دعم اللغتين العربية والإنجليزية
- إعدادات الشركة والشعار
- نسخ احتياطي واستعادة قاعدة البيانات
- واجهة عصرية بألوان غامقة

## متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج

```bash
python main.py
```

### 3. تسجيل الدخول الأولي

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: يُنصح بتغيير كلمة مرور المدير بعد أول تسجيل دخول.

## هيكل المشروع

```
warehouse-management/
├── main.py                 # نقطة البداية الرئيسية
├── database.py            # إدارة قاعدة البيانات
├── requirements.txt       # متطلبات Python
├── ui/                    # واجهات المستخدم
│   ├── main_window.py     # النافذة الرئيسية
│   ├── login_window.py    # نافذة تسجيل الدخول
│   ├── products_window.py # نافذة إدارة المنتجات
│   └── styles.py          # أنماط الواجهة
├── utils/                 # أدوات مساعدة
│   ├── translations.py    # نظام الترجمة
│   └── helpers.py         # دوال مساعدة
└── images/                # مجلد الصور (يُنشأ تلقائياً)
    └── products/          # صور المنتجات
```

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite مع الجداول التالية:

- **users**: المستخدمين والصلاحيات
- **categories**: تصنيفات المنتجات
- **units**: وحدات القياس
- **product_status**: حالات المنتجات
- **products**: المنتجات
- **suppliers**: الموردين
- **customers**: العملاء
- **purchase_invoices**: فواتير الشراء
- **purchase_invoice_items**: أصناف فواتير الشراء
- **sales_invoices**: فواتير المبيعات
- **sales_invoice_items**: أصناف فواتير المبيعات
- **settings**: إعدادات النظام

## الوظائف المتاحة حالياً

✅ **مكتملة**:
- نظام تسجيل الدخول والصلاحيات
- إدارة المنتجات الكاملة
- إدارة التصنيفات ووحدات القياس
- واجهة مستخدم عصرية
- دعم اللغتين العربية والإنجليزية
- قاعدة بيانات متكاملة

🚧 **قيد التطوير**:
- فواتير الشراء والمبيعات
- نظام التقارير
- إدارة المستخدمين
- النسخ الاحتياطي والاستعادة
- إعدادات النظام

## لقطات الشاشة

### نافذة تسجيل الدخول
واجهة تسجيل دخول أنيقة بتصميم عصري وألوان غامقة.

### النافذة الرئيسية
لوحة تحكم شاملة مع قائمة تنقل جانبية ومنطقة محتوى رئيسية.

### إدارة المنتجات
واجهة متكاملة لإدارة المنتجات مع إمكانية البحث والتصفية.

## المساهمة في التطوير

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة المطلوبة
3. تطبيق التغييرات مع اختبارها
4. إرسال Pull Request

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- إنشاء Issue جديد في GitHub
- التواصل عبر البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## شكر وتقدير

- PyQt6 لواجهة المستخدم الرائعة
- SQLite لقاعدة البيانات الموثوقة
- مجتمع Python للأدوات والمكتبات المفيدة

---

**تم تطوير هذا النظام بواسطة**: فريق إدارة المستودعات  
**الإصدار**: 1.0.0  
**تاريخ الإصدار**: 2024
