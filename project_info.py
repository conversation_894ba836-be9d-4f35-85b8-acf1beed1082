#!/usr/bin/env python3
"""
Project Information Display Script
عرض معلومات المشروع
"""

import os
import sys
from datetime import datetime

def print_header():
    """Print project header"""
    print("=" * 80)
    print("🏢 نظام إدارة المستودعات - Warehouse Management System")
    print("=" * 80)
    print()

def print_project_structure():
    """Print project structure"""
    print("📁 هيكل المشروع - Project Structure:")
    print("-" * 40)
    
    structure = {
        "main.py": "نقطة البداية الرئيسية - Main entry point",
        "database.py": "إدارة قاعدة البيانات - Database management",
        "requirements.txt": "متطلبات Python - Python requirements",
        "config.ini": "ملف التكوين - Configuration file",
        "README.md": "دليل المستخدم - User guide",
        "setup.py": "إعداد التوزيع - Distribution setup",
        "build_exe.py": "بناء ملف تنفيذي - Executable builder",
        "test_app.py": "اختبارات البرنامج - Application tests",
        "run_warehouse.bat": "تشغيل على Windows - Windows launcher",
        "run_warehouse.sh": "تشغيل على Linux/Mac - Linux/Mac launcher",
        "ui/": "واجهات المستخدم - User interfaces",
        "  ├── main_window.py": "النافذة الرئيسية - Main window",
        "  ├── login_window.py": "نافذة تسجيل الدخول - Login window",
        "  ├── products_window.py": "نافذة المنتجات - Products window",
        "  └── styles.py": "أنماط الواجهة - UI styles",
        "utils/": "أدوات مساعدة - Utility functions",
        "  ├── translations.py": "نظام الترجمة - Translation system",
        "  └── helpers.py": "دوال مساعدة - Helper functions"
    }
    
    for file_path, description in structure.items():
        if os.path.exists(file_path.split('/')[0]):
            status = "✅"
        else:
            status = "❌"
        print(f"{status} {file_path:<25} {description}")
    
    print()

def print_features():
    """Print implemented features"""
    print("🚀 المميزات المنجزة - Implemented Features:")
    print("-" * 40)
    
    features = [
        ("✅", "نظام تسجيل الدخول الآمن", "Secure login system"),
        ("✅", "إدارة صلاحيات المستخدمين", "User permissions management"),
        ("✅", "إدارة المنتجات الكاملة", "Complete product management"),
        ("✅", "إدارة التصنيفات", "Category management"),
        ("✅", "إدارة وحدات القياس", "Unit management"),
        ("✅", "إدارة حالات المنتجات", "Product status management"),
        ("✅", "رفع صور المنتجات", "Product image upload"),
        ("✅", "توليد باركود تلقائي", "Automatic barcode generation"),
        ("✅", "بحث متقدم", "Advanced search"),
        ("✅", "واجهة عصرية غامقة", "Modern dark interface"),
        ("✅", "دعم اللغتين العربية والإنجليزية", "Arabic/English support"),
        ("✅", "قاعدة بيانات SQLite", "SQLite database"),
        ("✅", "نظام اختبارات شامل", "Comprehensive testing system"),
        ("🚧", "فواتير الشراء والمبيعات", "Purchase/Sales invoices"),
        ("🚧", "نظام التقارير", "Reporting system"),
        ("🚧", "النسخ الاحتياطي", "Backup system"),
        ("🚧", "إدارة المستخدمين", "User management"),
        ("🚧", "الإعدادات المتقدمة", "Advanced settings")
    ]
    
    for status, feature_ar, feature_en in features:
        print(f"{status} {feature_ar} - {feature_en}")
    
    print()

def print_technical_specs():
    """Print technical specifications"""
    print("⚙️ المواصفات التقنية - Technical Specifications:")
    print("-" * 40)
    
    specs = [
        ("Framework", "PyQt6"),
        ("Database", "SQLite3"),
        ("Language", "Python 3.8+"),
        ("Architecture", "Desktop Application"),
        ("UI Theme", "Modern Dark Theme"),
        ("Localization", "Arabic/English"),
        ("Authentication", "bcrypt password hashing"),
        ("Image Support", "PNG, JPG, JPEG, BMP, GIF"),
        ("Barcode", "13-digit numeric codes"),
        ("Testing", "unittest framework"),
        ("Packaging", "PyInstaller support"),
        ("Platform", "Cross-platform (Windows/Linux/Mac)")
    ]
    
    for spec, value in specs:
        print(f"• {spec:<15}: {value}")
    
    print()

def print_database_schema():
    """Print database schema"""
    print("🗄️ مخطط قاعدة البيانات - Database Schema:")
    print("-" * 40)
    
    tables = [
        ("users", "المستخدمين - Users"),
        ("categories", "التصنيفات - Categories"),
        ("units", "وحدات القياس - Units"),
        ("product_status", "حالات المنتجات - Product Status"),
        ("products", "المنتجات - Products"),
        ("suppliers", "الموردين - Suppliers"),
        ("customers", "العملاء - Customers"),
        ("purchase_invoices", "فواتير الشراء - Purchase Invoices"),
        ("purchase_invoice_items", "أصناف فواتير الشراء - Purchase Items"),
        ("sales_invoices", "فواتير المبيعات - Sales Invoices"),
        ("sales_invoice_items", "أصناف فواتير المبيعات - Sales Items"),
        ("settings", "الإعدادات - Settings")
    ]
    
    for table, description in tables:
        print(f"📋 {table:<25} {description}")
    
    print()

def print_usage_instructions():
    """Print usage instructions"""
    print("📖 تعليمات الاستخدام - Usage Instructions:")
    print("-" * 40)
    
    print("1. تثبيت المتطلبات - Install Requirements:")
    print("   pip install -r requirements.txt")
    print()
    
    print("2. تشغيل البرنامج - Run Application:")
    print("   python main.py")
    print("   أو - or")
    print("   ./run_warehouse.bat (Windows)")
    print("   ./run_warehouse.sh (Linux/Mac)")
    print()
    
    print("3. تسجيل الدخول الأولي - Initial Login:")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    
    print("4. تشغيل الاختبارات - Run Tests:")
    print("   python test_app.py")
    print()
    
    print("5. بناء ملف تنفيذي - Build Executable:")
    print("   python build_exe.py")
    print()

def print_file_sizes():
    """Print file sizes"""
    print("📊 أحجام الملفات - File Sizes:")
    print("-" * 40)
    
    files_to_check = [
        "main.py", "database.py", "requirements.txt", "README.md",
        "ui/main_window.py", "ui/login_window.py", "ui/products_window.py",
        "ui/styles.py", "utils/translations.py", "utils/helpers.py"
    ]
    
    total_size = 0
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            size_kb = size / 1024
            print(f"📄 {file_path:<25} {size_kb:>8.1f} KB")
    
    print("-" * 40)
    print(f"📦 Total Size: {total_size / 1024:.1f} KB")
    print()

def print_statistics():
    """Print project statistics"""
    print("📈 إحصائيات المشروع - Project Statistics:")
    print("-" * 40)
    
    # Count lines of code
    total_lines = 0
    total_files = 0
    
    for root, dirs, files in os.walk('.'):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = len(f.readlines())
                        total_lines += lines
                        total_files += 1
                except:
                    pass
    
    print(f"📝 Python Files: {total_files}")
    print(f"📏 Lines of Code: {total_lines:,}")
    print(f"📅 Created: {datetime.now().strftime('%Y-%m-%d')}")
    print(f"🔧 Python Version: {sys.version.split()[0]}")
    
    # Check if database exists
    if os.path.exists('warehouse.db'):
        db_size = os.path.getsize('warehouse.db') / 1024
        print(f"🗄️ Database Size: {db_size:.1f} KB")
    
    print()

def main():
    """Main function"""
    print_header()
    print_project_structure()
    print_features()
    print_technical_specs()
    print_database_schema()
    print_usage_instructions()
    print_file_sizes()
    print_statistics()
    
    print("🎉 مشروع نظام إدارة المستودعات جاهز للاستخدام!")
    print("🎉 Warehouse Management System is ready to use!")
    print("=" * 80)

if __name__ == "__main__":
    main()
