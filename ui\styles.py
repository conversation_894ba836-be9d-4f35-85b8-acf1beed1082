"""
Modern dark theme styles for the warehouse management application
"""

# Color palette
COLORS = {
    'primary': '#2C3E50',      # Dark blue-gray
    'secondary': '#34495E',    # Lighter blue-gray
    'accent': '#3498DB',       # Blue
    'success': '#27AE60',      # Green
    'warning': '#F39C12',      # Orange
    'danger': '#E74C3C',       # Red
    'background': '#1E1E1E',   # Very dark gray
    'surface': '#2D2D2D',      # Dark gray
    'card': '#3A3A3A',         # Medium gray
    'text_primary': '#FFFFFF', # White
    'text_secondary': '#B0B0B0', # Light gray
    'border': '#4A4A4A',       # Gray border
    'hover': '#404040',        # Hover state
    'selected': '#0078D4',     # Selection blue
}

def get_main_style():
    """Get the main application stylesheet"""
    return f"""
    QMainWindow {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
    }}
    
    QWidget {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        font-family: 'Segoe UI', Arial, sans-serif;
        font-size: 10pt;
    }}
    
    /* Menu Bar */
    QMenuBar {{
        background-color: {COLORS['primary']};
        color: {COLORS['text_primary']};
        border: none;
        padding: 4px;
    }}
    
    QMenuBar::item {{
        background-color: transparent;
        padding: 8px 12px;
        margin: 2px;
        border-radius: 4px;
    }}
    
    QMenuBar::item:selected {{
        background-color: {COLORS['accent']};
    }}
    
    QMenu {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        padding: 4px;
    }}
    
    QMenu::item {{
        padding: 8px 20px;
        border-radius: 4px;
        margin: 1px;
    }}
    
    QMenu::item:selected {{
        background-color: {COLORS['accent']};
    }}
    
    /* Buttons */
    QPushButton {{
        background-color: {COLORS['accent']};
        color: {COLORS['text_primary']};
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: bold;
        min-width: 80px;
    }}
    
    QPushButton:hover {{
        background-color: #2980B9;
    }}
    
    QPushButton:pressed {{
        background-color: #21618C;
    }}
    
    QPushButton:disabled {{
        background-color: {COLORS['border']};
        color: {COLORS['text_secondary']};
    }}
    
    /* Primary Button */
    QPushButton[class="primary"] {{
        background-color: {COLORS['accent']};
    }}
    
    /* Success Button */
    QPushButton[class="success"] {{
        background-color: {COLORS['success']};
    }}
    
    QPushButton[class="success"]:hover {{
        background-color: #229954;
    }}
    
    /* Warning Button */
    QPushButton[class="warning"] {{
        background-color: {COLORS['warning']};
    }}
    
    QPushButton[class="warning"]:hover {{
        background-color: #D68910;
    }}
    
    /* Danger Button */
    QPushButton[class="danger"] {{
        background-color: {COLORS['danger']};
    }}
    
    QPushButton[class="danger"]:hover {{
        background-color: #C0392B;
    }}
    
    /* Input Fields */
    QLineEdit, QTextEdit, QPlainTextEdit {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 2px solid {COLORS['border']};
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 10pt;
    }}
    
    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
        border-color: {COLORS['accent']};
    }}
    
    /* ComboBox */
    QComboBox {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 2px solid {COLORS['border']};
        border-radius: 6px;
        padding: 8px 12px;
        min-width: 120px;
    }}
    
    QComboBox:focus {{
        border-color: {COLORS['accent']};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI0IwQjBCMCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    }}
    
    QComboBox QAbstractItemView {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        selection-background-color: {COLORS['accent']};
    }}
    
    /* Tables */
    QTableWidget {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        gridline-color: {COLORS['border']};
        selection-background-color: {COLORS['selected']};
    }}
    
    QTableWidget::item {{
        padding: 8px;
        border-bottom: 1px solid {COLORS['border']};
    }}
    
    QTableWidget::item:selected {{
        background-color: {COLORS['selected']};
    }}
    
    QHeaderView::section {{
        background-color: {COLORS['primary']};
        color: {COLORS['text_primary']};
        padding: 10px;
        border: none;
        border-right: 1px solid {COLORS['border']};
        font-weight: bold;
    }}
    
    /* Scroll Bars */
    QScrollBar:vertical {{
        background-color: {COLORS['surface']};
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COLORS['border']};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COLORS['text_secondary']};
    }}
    
    QScrollBar:horizontal {{
        background-color: {COLORS['surface']};
        height: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {COLORS['border']};
        border-radius: 6px;
        min-width: 20px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: {COLORS['text_secondary']};
    }}
    
    /* Group Box */
    QGroupBox {{
        background-color: {COLORS['card']};
        border: 2px solid {COLORS['border']};
        border-radius: 8px;
        margin-top: 10px;
        padding-top: 10px;
        font-weight: bold;
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 8px 0 8px;
        color: {COLORS['accent']};
    }}
    
    /* Tab Widget */
    QTabWidget::pane {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
    }}
    
    QTabBar::tab {{
        background-color: {COLORS['card']};
        color: {COLORS['text_primary']};
        padding: 10px 20px;
        margin-right: 2px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }}
    
    QTabBar::tab:selected {{
        background-color: {COLORS['accent']};
    }}
    
    QTabBar::tab:hover {{
        background-color: {COLORS['hover']};
    }}
    
    /* Status Bar */
    QStatusBar {{
        background-color: {COLORS['primary']};
        color: {COLORS['text_primary']};
        border-top: 1px solid {COLORS['border']};
    }}
    
    /* Tool Bar */
    QToolBar {{
        background-color: {COLORS['secondary']};
        border: none;
        spacing: 4px;
        padding: 4px;
    }}
    
    QToolButton {{
        background-color: transparent;
        color: {COLORS['text_primary']};
        border: none;
        padding: 8px;
        border-radius: 4px;
    }}
    
    QToolButton:hover {{
        background-color: {COLORS['hover']};
    }}
    
    QToolButton:pressed {{
        background-color: {COLORS['accent']};
    }}
    
    /* Splitter */
    QSplitter::handle {{
        background-color: {COLORS['border']};
    }}
    
    QSplitter::handle:horizontal {{
        width: 2px;
    }}
    
    QSplitter::handle:vertical {{
        height: 2px;
    }}
    
    /* Progress Bar */
    QProgressBar {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: 6px;
        text-align: center;
        color: {COLORS['text_primary']};
    }}
    
    QProgressBar::chunk {{
        background-color: {COLORS['accent']};
        border-radius: 5px;
    }}
    
    /* Check Box */
    QCheckBox {{
        color: {COLORS['text_primary']};
        spacing: 8px;
    }}
    
    QCheckBox::indicator {{
        width: 18px;
        height: 18px;
        border: 2px solid {COLORS['border']};
        border-radius: 4px;
        background-color: {COLORS['surface']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {COLORS['accent']};
        border-color: {COLORS['accent']};
    }}
    
    /* Radio Button */
    QRadioButton {{
        color: {COLORS['text_primary']};
        spacing: 8px;
    }}
    
    QRadioButton::indicator {{
        width: 18px;
        height: 18px;
        border: 2px solid {COLORS['border']};
        border-radius: 9px;
        background-color: {COLORS['surface']};
    }}
    
    QRadioButton::indicator:checked {{
        background-color: {COLORS['accent']};
        border-color: {COLORS['accent']};
    }}
    
    /* Date Edit */
    QDateEdit {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 2px solid {COLORS['border']};
        border-radius: 6px;
        padding: 8px 12px;
    }}
    
    QDateEdit:focus {{
        border-color: {COLORS['accent']};
    }}
    
    /* Spin Box */
    QSpinBox, QDoubleSpinBox {{
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        border: 2px solid {COLORS['border']};
        border-radius: 6px;
        padding: 8px 12px;
    }}
    
    QSpinBox:focus, QDoubleSpinBox:focus {{
        border-color: {COLORS['accent']};
    }}
    """

def get_login_style():
    """Get stylesheet for login window"""
    return get_main_style() + f"""
    QDialog {{
        background-color: {COLORS['background']};
    }}
    
    QLabel[class="title"] {{
        font-size: 24pt;
        font-weight: bold;
        color: {COLORS['accent']};
        margin: 20px 0;
    }}
    
    QLabel[class="subtitle"] {{
        font-size: 12pt;
        color: {COLORS['text_secondary']};
        margin-bottom: 30px;
    }}
    """

def get_card_style():
    """Get stylesheet for card-like containers"""
    return f"""
    QFrame[class="card"] {{
        background-color: {COLORS['card']};
        border: 1px solid {COLORS['border']};
        border-radius: 8px;
        padding: 16px;
        margin: 8px;
    }}
    """
