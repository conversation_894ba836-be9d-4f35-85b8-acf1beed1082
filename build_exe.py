#!/usr/bin/env python3
"""
Build script to create executable file using PyInstaller
"""

import os
import sys
import subprocess
import shutil

def build_executable():
    """Build executable using PyInstaller"""
    
    print("🔨 Building Warehouse Management System executable...")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Create single executable file
        "--windowed",                   # Hide console window (for GUI apps)
        "--name=WarehouseManagement",   # Executable name
        "--icon=assets/icon.ico",       # Icon file (if exists)
        "--add-data=ui;ui",             # Include UI folder
        "--add-data=utils;utils",       # Include utils folder
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=sqlite3",
        "--hidden-import=bcrypt",
        "--hidden-import=PIL",
        "--hidden-import=reportlab",
        "--hidden-import=openpyxl",
        "--hidden-import=barcode",
        "--hidden-import=qrcode",
        "main.py"
    ]
    
    # Remove icon parameter if icon file doesn't exist
    if not os.path.exists("assets/icon.ico"):
        cmd = [arg for arg in cmd if not arg.startswith("--icon")]
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        print(f"📁 Executable created in: dist/WarehouseManagement.exe")
        
        # Create distribution folder
        dist_folder = "distribution"
        if os.path.exists(dist_folder):
            shutil.rmtree(dist_folder)
        os.makedirs(dist_folder)
        
        # Copy executable
        exe_source = "dist/WarehouseManagement.exe"
        exe_dest = os.path.join(dist_folder, "WarehouseManagement.exe")
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, exe_dest)
            print(f"📦 Executable copied to: {exe_dest}")
        
        # Copy README and other files
        files_to_copy = ["README.md", "requirements.txt"]
        for file_name in files_to_copy:
            if os.path.exists(file_name):
                shutil.copy2(file_name, dist_folder)
                print(f"📄 Copied: {file_name}")
        
        # Create installation instructions
        create_install_instructions(dist_folder)
        
        print("\n🎉 Distribution package created successfully!")
        print(f"📁 Package location: {os.path.abspath(dist_folder)}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

def create_install_instructions(dist_folder):
    """Create installation instructions file"""
    
    instructions = """
# تعليمات التثبيت - Installation Instructions

## العربية

### متطلبات النظام:
- نظام التشغيل: Windows 10 أو أحدث
- مساحة فارغة: 100 ميجابايت على الأقل

### خطوات التثبيت:
1. انسخ ملف WarehouseManagement.exe إلى المجلد المطلوب
2. قم بتشغيل البرنامج بالنقر المزدوج على الملف
3. عند أول تشغيل، استخدم:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

### ملاحظات مهمة:
- يُنصح بتغيير كلمة مرور المدير بعد أول تسجيل دخول
- سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
- يمكن نسخ البرنامج على عدة أجهزة في نفس الشبكة

---

## English

### System Requirements:
- Operating System: Windows 10 or newer
- Free Space: At least 100 MB

### Installation Steps:
1. Copy WarehouseManagement.exe to desired folder
2. Run the program by double-clicking the file
3. For first login, use:
   - Username: admin
   - Password: admin123

### Important Notes:
- Recommended to change admin password after first login
- Database will be created automatically on first run
- Program can be copied to multiple computers on same network

---

## الدعم الفني - Technical Support

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
For technical support or to report issues:

- Email: <EMAIL>
- GitHub: https://github.com/warehouse-management/warehouse-system

---

الإصدار: 1.0.0
Version: 1.0.0
"""
    
    instructions_file = os.path.join(dist_folder, "تعليمات_التثبيت_Installation_Instructions.txt")
    with open(instructions_file, "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print(f"📋 Installation instructions created: {instructions_file}")

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("✅ PyInstaller is already installed")
        return True
    except ImportError:
        print("📦 Installing PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False

def main():
    """Main build function"""
    print("🏗️  Warehouse Management System - Build Script")
    print("=" * 50)
    
    # Check if PyInstaller is available
    if not install_pyinstaller():
        print("❌ Cannot proceed without PyInstaller")
        return 1
    
    # Build executable
    if build_executable():
        print("\n🎉 Build process completed successfully!")
        return 0
    else:
        print("\n❌ Build process failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
