import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QMenu, QStatusBar, QToolBar, QTabWidget,
                            QLabel, QPushButton, QFrame, QGridLayout, QMessageBox,
                            QApplication, QSplitter, QTreeWidget, QTreeWidgetItem,
                            QStackedWidget, QDialog)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont
from database import DatabaseManager
from utils.translations import tr, translator
from ui.styles import get_main_style, COLORS
from ui.login_window import LoginWindow
from ui.products_window import ProductsWindow

class DashboardWidget(QWidget):
    """Dashboard widget showing summary information"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db = db_manager
        self.init_ui()
        
    def init_ui(self):
        layout = QGridLayout()
        
        # Create summary cards
        self.create_summary_cards(layout)
        
        self.setLayout(layout)
        
    def create_summary_cards(self, layout):
        """Create summary information cards"""
        
        # Products count card
        products_card = self.create_card(tr('products'), "0", COLORS['accent'])
        layout.addWidget(products_card, 0, 0)
        
        # Low stock card
        low_stock_card = self.create_card(tr('low_stock', 'Low Stock'), "0", COLORS['warning'])
        layout.addWidget(low_stock_card, 0, 1)
        
        # Today's sales card
        sales_card = self.create_card(tr('today_sales', "Today's Sales"), "0", COLORS['success'])
        layout.addWidget(sales_card, 0, 2)
        
        # Today's purchases card
        purchases_card = self.create_card(tr('today_purchases', "Today's Purchases"), "0", COLORS['primary'])
        layout.addWidget(purchases_card, 1, 0)
        
        # Total value card
        value_card = self.create_card(tr('total_value', 'Total Inventory Value'), "0", COLORS['secondary'])
        layout.addWidget(value_card, 1, 1)
        
        # Users card
        users_card = self.create_card(tr('users'), "0", COLORS['danger'])
        layout.addWidget(users_card, 1, 2)
        
    def create_card(self, title, value, color):
        """Create a summary card widget"""
        card = QFrame()
        card.setProperty("class", "card")
        card.setStyleSheet(f"""
            QFrame[class="card"] {{
                background-color: {COLORS['card']};
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                padding: 20px;
                margin: 8px;
                min-height: 120px;
            }}
            QFrame[class="card"]:hover {{
                border-color: {color};
            }}
        """)
        
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet(f"color: {COLORS['text_secondary']}; font-size: 12pt; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel(value)
        value_label.setStyleSheet(f"color: {color}; font-size: 24pt; font-weight: bold;")
        layout.addWidget(value_label)
        
        card.setLayout(layout)
        return card

class NavigationWidget(QTreeWidget):
    """Navigation tree widget"""
    
    item_selected = pyqtSignal(str)
    
    def __init__(self, user_role):
        super().__init__()
        self.user_role = user_role
        self.init_ui()
        
    def init_ui(self):
        self.setHeaderHidden(True)
        self.setRootIsDecorated(True)
        self.setIndentation(20)
        
        # Create navigation items based on user role
        self.create_navigation_items()
        
        # Connect selection signal
        self.itemClicked.connect(self.on_item_clicked)
        
    def create_navigation_items(self):
        """Create navigation tree items based on user role"""
        
        # Dashboard (available to all)
        dashboard_item = QTreeWidgetItem([tr('dashboard')])
        dashboard_item.setData(0, Qt.ItemDataRole.UserRole, 'dashboard')
        self.addTopLevelItem(dashboard_item)
        
        # Products (available to admin and warehouse_staff)
        if self.user_role in ['admin', 'warehouse_staff']:
            products_item = QTreeWidgetItem([tr('products')])
            products_item.setData(0, Qt.ItemDataRole.UserRole, 'products')
            self.addTopLevelItem(products_item)
        
        # Invoices
        invoices_item = QTreeWidgetItem([tr('invoices', 'Invoices')])
        self.addTopLevelItem(invoices_item)
        
        # Purchase invoices (available to admin and warehouse_staff)
        if self.user_role in ['admin', 'warehouse_staff']:
            purchase_item = QTreeWidgetItem([tr('purchase_invoices')])
            purchase_item.setData(0, Qt.ItemDataRole.UserRole, 'purchase_invoices')
            invoices_item.addChild(purchase_item)
        
        # Sales invoices (available to all)
        sales_item = QTreeWidgetItem([tr('sales_invoices')])
        sales_item.setData(0, Qt.ItemDataRole.UserRole, 'sales_invoices')
        invoices_item.addChild(sales_item)
        
        # Reports (available to all)
        reports_item = QTreeWidgetItem([tr('reports')])
        reports_item.setData(0, Qt.ItemDataRole.UserRole, 'reports')
        self.addTopLevelItem(reports_item)
        
        # Users (admin only)
        if self.user_role == 'admin':
            users_item = QTreeWidgetItem([tr('users')])
            users_item.setData(0, Qt.ItemDataRole.UserRole, 'users')
            self.addTopLevelItem(users_item)
        
        # Settings (admin only)
        if self.user_role == 'admin':
            settings_item = QTreeWidgetItem([tr('settings')])
            settings_item.setData(0, Qt.ItemDataRole.UserRole, 'settings')
            self.addTopLevelItem(settings_item)
        
        # Expand all items
        self.expandAll()
        
        # Select dashboard by default
        self.setCurrentItem(dashboard_item)
        
    def on_item_clicked(self, item, column):
        """Handle navigation item click"""
        _ = column  # Unused parameter
        page_name = item.data(0, Qt.ItemDataRole.UserRole)
        if page_name:
            self.item_selected.emit(page_name)

class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db = DatabaseManager()
        self.current_language = self.db.get_setting('language') or 'ar'
        
        # Set translator language
        translator.set_language(self.current_language)
        
        self.init_ui()
        self.apply_styles()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(tr('app_title'))
        self.setMinimumSize(1200, 800)
        
        # Center the window
        self.center_window()
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create tool bar
        self.create_tool_bar()
        
        # Create status bar
        self.create_status_bar()
        
        # Create central widget
        self.create_central_widget()
        
        # Update status bar with user info
        self.update_status_bar()
        
    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu(tr('file'))
        
        # Logout action
        logout_action = QAction(tr('logout'), self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction(tr('exit'), self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu (admin only)
        if self.user_data['role'] == 'admin':
            tools_menu = menubar.addMenu(tr('tools'))
            
            # Backup action
            backup_action = QAction(tr('create_backup'), self)
            backup_action.triggered.connect(self.create_backup)
            tools_menu.addAction(backup_action)
            
            # Restore action
            restore_action = QAction(tr('restore_backup'), self)
            restore_action.triggered.connect(self.restore_backup)
            tools_menu.addAction(restore_action)
        
        # Help menu
        help_menu = menubar.addMenu(tr('help'))
        
        # About action
        about_action = QAction(tr('about'), self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_tool_bar(self):
        """Create the tool bar"""
        toolbar = self.addToolBar('Main')
        toolbar.setMovable(False)
        
        # Add some common actions to toolbar
        # These will be implemented later
        pass
        
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = self.statusBar()
        
        # User info label
        self.user_label = QLabel()
        self.status_bar.addPermanentWidget(self.user_label)
        
        # Language label
        self.language_label = QLabel()
        self.status_bar.addPermanentWidget(self.language_label)
        
    def create_central_widget(self):
        """Create the central widget with navigation and content"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Navigation panel
        nav_frame = QFrame()
        nav_frame.setFixedWidth(250)
        nav_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border-right: 1px solid {COLORS['border']};
            }}
        """)
        
        nav_layout = QVBoxLayout()
        nav_layout.setContentsMargins(10, 10, 10, 10)
        
        # User info section
        user_info_frame = QFrame()
        user_info_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['card']};
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
            }}
        """)
        
        user_info_layout = QVBoxLayout()
        
        # Welcome message
        welcome_label = QLabel(f"{tr('welcome')}, {self.user_data['full_name']}")
        welcome_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: bold; font-size: 11pt;")
        user_info_layout.addWidget(welcome_label)
        
        # Role label
        role_label = QLabel(tr(self.user_data['role']))
        role_label.setStyleSheet(f"color: {COLORS['text_secondary']}; font-size: 9pt;")
        user_info_layout.addWidget(role_label)
        
        user_info_frame.setLayout(user_info_layout)
        nav_layout.addWidget(user_info_frame)
        
        # Navigation tree
        self.navigation = NavigationWidget(self.user_data['role'])
        self.navigation.item_selected.connect(self.show_page)
        nav_layout.addWidget(self.navigation)
        
        nav_frame.setLayout(nav_layout)
        splitter.addWidget(nav_frame)
        
        # Content area
        self.content_stack = QStackedWidget()
        
        # Add dashboard as default page
        self.dashboard = DashboardWidget(self.db)
        self.content_stack.addWidget(self.dashboard)

        # Initialize page widgets dictionary
        self.page_widgets = {
            'dashboard': self.dashboard
        }
        
        splitter.addWidget(self.content_stack)
        
        # Set splitter proportions
        splitter.setSizes([250, 950])
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
        
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def apply_styles(self):
        """Apply the main window styles"""
        self.setStyleSheet(get_main_style())
        
    def show_page(self, page_name):
        """Show the selected page"""
        # Check if page widget already exists
        if page_name in self.page_widgets:
            widget = self.page_widgets[page_name]
            self.content_stack.setCurrentWidget(widget)
            return

        # Create new page widget based on page name
        widget = None

        if page_name == 'products':
            widget = ProductsWindow(self.db, self.user_data['role'])
        elif page_name == 'purchase_invoices':
            # Will be implemented later
            widget = QLabel(f"صفحة {tr('purchase_invoices')} قيد التطوير")
            widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            widget.setStyleSheet(f"font-size: 18pt; color: {COLORS['text_secondary']};")
        elif page_name == 'sales_invoices':
            # Will be implemented later
            widget = QLabel(f"صفحة {tr('sales_invoices')} قيد التطوير")
            widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            widget.setStyleSheet(f"font-size: 18pt; color: {COLORS['text_secondary']};")
        elif page_name == 'reports':
            # Will be implemented later
            widget = QLabel(f"صفحة {tr('reports')} قيد التطوير")
            widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            widget.setStyleSheet(f"font-size: 18pt; color: {COLORS['text_secondary']};")
        elif page_name == 'users':
            # Will be implemented later
            widget = QLabel(f"صفحة {tr('users')} قيد التطوير")
            widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            widget.setStyleSheet(f"font-size: 18pt; color: {COLORS['text_secondary']};")
        elif page_name == 'settings':
            # Will be implemented later
            widget = QLabel(f"صفحة {tr('settings')} قيد التطوير")
            widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            widget.setStyleSheet(f"font-size: 18pt; color: {COLORS['text_secondary']};")
        else:
            # Default case
            widget = QLabel(f"صفحة غير معروفة: {page_name}")
            widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            widget.setStyleSheet(f"font-size: 18pt; color: {COLORS['danger']};")

        if widget:
            # Add to stack and dictionary
            self.content_stack.addWidget(widget)
            self.page_widgets[page_name] = widget
            self.content_stack.setCurrentWidget(widget)
        
    def update_status_bar(self):
        """Update status bar information"""
        # User info
        user_text = f"{tr('user', 'User')}: {self.user_data['username']} ({tr(self.user_data['role'])})"
        self.user_label.setText(user_text)
        
        # Language info
        lang_text = f"{tr('language')}: {tr('arabic') if self.current_language == 'ar' else tr('english')}"
        self.language_label.setText(lang_text)
        
    def logout(self):
        """Handle user logout"""
        reply = QMessageBox.question(
            self, 
            tr('logout'), 
            tr('confirm_logout', 'Are you sure you want to logout?'),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.close()
            # Show login window again
            login_window = LoginWindow()
            if login_window.exec() == QDialog.DialogCode.Accepted:
                # Create new main window with new user
                pass
                
    def create_backup(self):
        """Create database backup"""
        # This will be implemented later
        QMessageBox.information(self, tr('backup'), tr('backup_feature_coming_soon', 'Backup feature coming soon'))
        
    def restore_backup(self):
        """Restore database backup"""
        # This will be implemented later
        QMessageBox.information(self, tr('restore'), tr('restore_feature_coming_soon', 'Restore feature coming soon'))
        
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            tr('about'),
            f"""
            <h3>{tr('app_title')}</h3>
            <p>{tr('version', 'Version')}: 1.0</p>
            <p>{tr('developed_by', 'Developed by')}: Warehouse Management Team</p>
            <p>{tr('description', 'A comprehensive warehouse management system')}</p>
            """
        )
        
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(
            self,
            tr('exit'),
            tr('confirm_exit', 'Are you sure you want to exit?'),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Test user data
    test_user = {
        'id': 1,
        'username': 'admin',
        'role': 'admin',
        'full_name': 'مدير النظام'
    }
    
    window = MainWindow(test_user)
    window.show()
    
    sys.exit(app.exec())
